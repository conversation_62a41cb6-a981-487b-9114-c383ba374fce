const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const Product = require('../models/Product');
const { auth } = require('../middleware/auth');
const { uploadAvatar } = require('../config/cloudinary');

const router = express.Router();

// Get user profile
router.get('/profile', auth, async (req, res) => {
    try {
        const user = await User.findById(req.user.id)
            .populate('cart.product', 'name images basePrice salePrice totalStock')
            .populate('wishlist', 'name images basePrice salePrice totalStock')
            .populate('orders', 'orderNumber total status createdAt');

        res.json({
            success: true,
            data: { user }
        });

    } catch (error) {
        console.error('Get profile error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get user profile',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Update user profile
router.put('/profile', auth, uploadAvatar.single('avatar'), [
    body('name').optional().trim().isLength({ min: 2 }).withMessage('Name must be at least 2 characters'),
    body('phone').optional().isMobilePhone('en-IN').withMessage('Please provide a valid phone number'),
    body('preferences.newsletter').optional().isBoolean().withMessage('Newsletter preference must be a boolean'),
    body('preferences.smsNotifications').optional().isBoolean().withMessage('SMS notifications preference must be a boolean'),
    body('preferences.emailNotifications').optional().isBoolean().withMessage('Email notifications preference must be a boolean')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const updateData = { ...req.body };

        // Handle avatar upload
        if (req.file) {
            updateData.avatar = req.file.path;
        }

        // Parse preferences if provided as string
        if (updateData.preferences && typeof updateData.preferences === 'string') {
            updateData.preferences = JSON.parse(updateData.preferences);
        }

        const user = await User.findByIdAndUpdate(
            req.user.id,
            updateData,
            { new: true, runValidators: true }
        ).select('-password');

        res.json({
            success: true,
            message: 'Profile updated successfully',
            data: { user }
        });

    } catch (error) {
        console.error('Update profile error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update profile',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Add to cart
router.post('/cart', auth, [
    body('productId').isMongoId().withMessage('Valid product ID is required'),
    body('quantity').isInt({ min: 1 }).withMessage('Quantity must be at least 1'),
    body('size').optional().trim(),
    body('color').optional().trim()
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { productId, quantity, size, color } = req.body;

        // Verify product exists and is active
        const product = await Product.findById(productId);
        if (!product || !product.isActive) {
            return res.status(404).json({
                success: false,
                message: 'Product not found or inactive'
            });
        }

        // Check stock availability
        if (product.totalStock < quantity) {
            return res.status(400).json({
                success: false,
                message: `Insufficient stock. Available: ${product.totalStock}`
            });
        }

        const user = await User.findById(req.user.id);

        // Check if product already in cart
        const existingItemIndex = user.cart.findIndex(
            item => item.product.toString() === productId &&
                   item.size === size &&
                   item.color === color
        );

        if (existingItemIndex > -1) {
            // Update quantity
            user.cart[existingItemIndex].quantity += quantity;
        } else {
            // Add new item
            user.cart.push({
                product: productId,
                quantity,
                size,
                color
            });
        }

        await user.save();

        // Populate cart for response
        await user.populate('cart.product', 'name images basePrice salePrice totalStock');

        res.json({
            success: true,
            message: 'Product added to cart',
            data: { cart: user.cart }
        });

    } catch (error) {
        console.error('Add to cart error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to add product to cart',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Update cart item
router.put('/cart/:itemId', auth, [
    body('quantity').isInt({ min: 1 }).withMessage('Quantity must be at least 1')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { itemId } = req.params;
        const { quantity } = req.body;

        const user = await User.findById(req.user.id);
        const cartItem = user.cart.id(itemId);

        if (!cartItem) {
            return res.status(404).json({
                success: false,
                message: 'Cart item not found'
            });
        }

        // Verify stock availability
        const product = await Product.findById(cartItem.product);
        if (product.totalStock < quantity) {
            return res.status(400).json({
                success: false,
                message: `Insufficient stock. Available: ${product.totalStock}`
            });
        }

        cartItem.quantity = quantity;
        await user.save();

        // Populate cart for response
        await user.populate('cart.product', 'name images basePrice salePrice totalStock');

        res.json({
            success: true,
            message: 'Cart updated successfully',
            data: { cart: user.cart }
        });

    } catch (error) {
        console.error('Update cart error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update cart',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Remove from cart
router.delete('/cart/:itemId', auth, async (req, res) => {
    try {
        const { itemId } = req.params;

        const user = await User.findById(req.user.id);
        user.cart.id(itemId).remove();
        await user.save();

        // Populate cart for response
        await user.populate('cart.product', 'name images basePrice salePrice totalStock');

        res.json({
            success: true,
            message: 'Item removed from cart',
            data: { cart: user.cart }
        });

    } catch (error) {
        console.error('Remove from cart error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to remove item from cart',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Clear cart
router.delete('/cart', auth, async (req, res) => {
    try {
        await User.findByIdAndUpdate(req.user.id, { cart: [] });

        res.json({
            success: true,
            message: 'Cart cleared successfully',
            data: { cart: [] }
        });

    } catch (error) {
        console.error('Clear cart error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to clear cart',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Add to wishlist
router.post('/wishlist', auth, [
    body('productId').isMongoId().withMessage('Valid product ID is required')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { productId } = req.body;

        // Verify product exists
        const product = await Product.findById(productId);
        if (!product || !product.isActive) {
            return res.status(404).json({
                success: false,
                message: 'Product not found or inactive'
            });
        }

        const user = await User.findById(req.user.id);

        // Check if already in wishlist
        if (user.wishlist.includes(productId)) {
            return res.status(400).json({
                success: false,
                message: 'Product already in wishlist'
            });
        }

        user.wishlist.push(productId);
        await user.save();

        // Populate wishlist for response
        await user.populate('wishlist', 'name images basePrice salePrice totalStock');

        res.json({
            success: true,
            message: 'Product added to wishlist',
            data: { wishlist: user.wishlist }
        });

    } catch (error) {
        console.error('Add to wishlist error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to add product to wishlist',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Remove from wishlist
router.delete('/wishlist/:productId', auth, async (req, res) => {
    try {
        const { productId } = req.params;

        const user = await User.findByIdAndUpdate(
            req.user.id,
            { $pull: { wishlist: productId } },
            { new: true }
        );

        // Populate wishlist for response
        await user.populate('wishlist', 'name images basePrice salePrice totalStock');

        res.json({
            success: true,
            message: 'Product removed from wishlist',
            data: { wishlist: user.wishlist }
        });

    } catch (error) {
        console.error('Remove from wishlist error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to remove product from wishlist',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Add address
router.post('/addresses', auth, [
    body('fullName').trim().isLength({ min: 2 }).withMessage('Full name is required'),
    body('phone').isMobilePhone('en-IN').withMessage('Valid phone number is required'),
    body('addressLine1').trim().isLength({ min: 5 }).withMessage('Address line 1 is required'),
    body('city').trim().isLength({ min: 2 }).withMessage('City is required'),
    body('state').trim().isLength({ min: 2 }).withMessage('State is required'),
    body('pincode').isLength({ min: 6, max: 6 }).withMessage('Valid pincode is required'),
    body('type').optional().isIn(['home', 'work', 'other']).withMessage('Invalid address type')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const addressData = req.body;
        const user = await User.findById(req.user.id);

        // If this is the first address or marked as default, make it default
        if (user.addresses.length === 0 || addressData.isDefault) {
            // Remove default from other addresses
            user.addresses.forEach(addr => addr.isDefault = false);
            addressData.isDefault = true;
        }

        user.addresses.push(addressData);
        await user.save();

        res.json({
            success: true,
            message: 'Address added successfully',
            data: { addresses: user.addresses }
        });

    } catch (error) {
        console.error('Add address error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to add address',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Update address
router.put('/addresses/:addressId', auth, async (req, res) => {
    try {
        const { addressId } = req.params;
        const updateData = req.body;

        const user = await User.findById(req.user.id);
        const address = user.addresses.id(addressId);

        if (!address) {
            return res.status(404).json({
                success: false,
                message: 'Address not found'
            });
        }

        // If marking as default, remove default from others
        if (updateData.isDefault) {
            user.addresses.forEach(addr => addr.isDefault = false);
        }

        Object.assign(address, updateData);
        await user.save();

        res.json({
            success: true,
            message: 'Address updated successfully',
            data: { addresses: user.addresses }
        });

    } catch (error) {
        console.error('Update address error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update address',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Delete address
router.delete('/addresses/:addressId', auth, async (req, res) => {
    try {
        const { addressId } = req.params;

        const user = await User.findById(req.user.id);
        const address = user.addresses.id(addressId);

        if (!address) {
            return res.status(404).json({
                success: false,
                message: 'Address not found'
            });
        }

        const wasDefault = address.isDefault;
        address.remove();

        // If deleted address was default, make first remaining address default
        if (wasDefault && user.addresses.length > 0) {
            user.addresses[0].isDefault = true;
        }

        await user.save();

        res.json({
            success: true,
            message: 'Address deleted successfully',
            data: { addresses: user.addresses }
        });

    } catch (error) {
        console.error('Delete address error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete address',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

module.exports = router;