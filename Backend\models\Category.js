const mongoose = require('mongoose');
const slugify = require('slugify');

const categorySchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        trim: true,
        unique: true
    },
    slug: {
        type: String,
        unique: true
    },
    description: {
        type: String,
        trim: true
    },
    image: {
        url: String,
        alt: String
    },
    icon: String, // Icon class or emoji
    parent: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Category',
        default: null
    },
    children: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Category'
    }],
    level: {
        type: Number,
        default: 0
    },
    sortOrder: {
        type: Number,
        default: 0
    },
    isActive: {
        type: Boolean,
        default: true
    },
    isFeatured: {
        type: Boolean,
        default: false
    },
    productCount: {
        type: Number,
        default: 0
    },
    seo: {
        metaTitle: String,
        metaDescription: String,
        keywords: [String]
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }
}, {
    timestamps: true
});

// Indexes
categorySchema.index({ name: 1 });
categorySchema.index({ slug: 1 });
categorySchema.index({ parent: 1 });
categorySchema.index({ isActive: 1 });
categorySchema.index({ isFeatured: 1 });
categorySchema.index({ sortOrder: 1 });

// Generate slug before saving
categorySchema.pre('save', function(next) {
    if (this.isModified('name')) {
        this.slug = slugify(this.name, { lower: true, strict: true });
    }
    next();
});

// Update parent's children array
categorySchema.post('save', async function() {
    if (this.parent) {
        await this.constructor.findByIdAndUpdate(
            this.parent,
            { $addToSet: { children: this._id } }
        );
    }
});

// Remove from parent's children array when deleted
categorySchema.post('remove', async function() {
    if (this.parent) {
        await this.constructor.findByIdAndUpdate(
            this.parent,
            { $pull: { children: this._id } }
        );
    }
});

// Get full category path
categorySchema.methods.getPath = async function() {
    const path = [this.name];
    let current = this;
    
    while (current.parent) {
        current = await this.constructor.findById(current.parent);
        if (current) {
            path.unshift(current.name);
        }
    }
    
    return path.join(' > ');
};

// Get all descendants
categorySchema.methods.getDescendants = async function() {
    const descendants = [];
    
    const getChildren = async (categoryId) => {
        const children = await this.constructor.find({ parent: categoryId });
        for (const child of children) {
            descendants.push(child);
            await getChildren(child._id);
        }
    };
    
    await getChildren(this._id);
    return descendants;
};

module.exports = mongoose.model('Category', categorySchema);
