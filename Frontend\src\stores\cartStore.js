import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { userAPI } from '../services/api';
import toast from 'react-hot-toast';

const useCartStore = create(
  persist(
    (set, get) => ({
      // State
      items: [],
      isLoading: false,
      error: null,
      isOpen: false,

      // Actions
      addToCart: async (product, quantity = 1, size = null, color = null) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await userAPI.addToCart({
            productId: product._id,
            quantity,
            size,
            color
          });

          set({
            items: response.data.data.cart,
            isLoading: false,
            error: null
          });

          toast.success(`${product.name} added to cart!`);
          return { success: true };
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Failed to add to cart';
          set({ isLoading: false, error: errorMessage });
          toast.error(errorMessage);
          return { success: false, error: errorMessage };
        }
      },

      updateCartItem: async (itemId, quantity) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await userAPI.updateCartItem(itemId, { quantity });
          
          set({
            items: response.data.data.cart,
            isLoading: false,
            error: null
          });

          return { success: true };
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Failed to update cart';
          set({ isLoading: false, error: errorMessage });
          toast.error(errorMessage);
          return { success: false, error: errorMessage };
        }
      },

      removeFromCart: async (itemId) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await userAPI.removeFromCart(itemId);
          
          set({
            items: response.data.data.cart,
            isLoading: false,
            error: null
          });

          toast.success('Item removed from cart');
          return { success: true };
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Failed to remove from cart';
          set({ isLoading: false, error: errorMessage });
          toast.error(errorMessage);
          return { success: false, error: errorMessage };
        }
      },

      clearCart: async () => {
        set({ isLoading: true, error: null });
        
        try {
          await userAPI.clearCart();
          
          set({
            items: [],
            isLoading: false,
            error: null
          });

          toast.success('Cart cleared');
          return { success: true };
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Failed to clear cart';
          set({ isLoading: false, error: errorMessage });
          toast.error(errorMessage);
          return { success: false, error: errorMessage };
        }
      },

      // Local cart management (for non-authenticated users)
      addToLocalCart: (product, quantity = 1, size = null, color = null) => {
        const { items } = get();
        const existingItemIndex = items.findIndex(
          item => item.product._id === product._id && 
                 item.size === size && 
                 item.color === color
        );

        let newItems;
        if (existingItemIndex > -1) {
          newItems = items.map((item, index) =>
            index === existingItemIndex
              ? { ...item, quantity: item.quantity + quantity }
              : item
          );
        } else {
          newItems = [
            ...items,
            {
              _id: `local_${Date.now()}`,
              product,
              quantity,
              size,
              color,
              addedAt: new Date()
            }
          ];
        }

        set({ items: newItems });
        toast.success(`${product.name} added to cart!`);
      },

      updateLocalCartItem: (itemId, quantity) => {
        const { items } = get();
        const newItems = items.map(item =>
          item._id === itemId ? { ...item, quantity } : item
        );
        set({ items: newItems });
      },

      removeFromLocalCart: (itemId) => {
        const { items } = get();
        const newItems = items.filter(item => item._id !== itemId);
        set({ items: newItems });
        toast.success('Item removed from cart');
      },

      clearLocalCart: () => {
        set({ items: [] });
        toast.success('Cart cleared');
      },

      // Cart UI
      openCart: () => set({ isOpen: true }),
      closeCart: () => set({ isOpen: false }),
      toggleCart: () => set(state => ({ isOpen: !state.isOpen })),

      // Sync cart with server when user logs in
      syncCart: async (serverCart) => {
        const { items: localItems } = get();
        
        if (localItems.length > 0) {
          // Merge local cart with server cart
          try {
            for (const localItem of localItems) {
              await userAPI.addToCart({
                productId: localItem.product._id,
                quantity: localItem.quantity,
                size: localItem.size,
                color: localItem.color
              });
            }
            
            // Get updated cart from server
            const response = await userAPI.getProfile();
            set({ items: response.data.data.user.cart });
          } catch (error) {
            console.error('Failed to sync cart:', error);
            // Fallback to server cart
            set({ items: serverCart });
          }
        } else {
          set({ items: serverCart });
        }
      },

      // Load cart from server
      loadCart: async () => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await userAPI.getProfile();
          set({
            items: response.data.data.user.cart,
            isLoading: false,
            error: null
          });
          return { success: true };
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Failed to load cart';
          set({ isLoading: false, error: errorMessage });
          return { success: false, error: errorMessage };
        }
      },

      // Getters
      getCartTotal: () => {
        const { items } = get();
        return items.reduce((total, item) => {
          const price = item.product.salePrice || item.product.basePrice;
          return total + (price * item.quantity);
        }, 0);
      },

      getCartCount: () => {
        const { items } = get();
        return items.reduce((count, item) => count + item.quantity, 0);
      },

      getCartSubtotal: () => {
        const { items } = get();
        return items.reduce((subtotal, item) => {
          const price = item.product.salePrice || item.product.basePrice;
          return subtotal + (price * item.quantity);
        }, 0);
      },

      getShippingCost: () => {
        const subtotal = get().getCartSubtotal();
        return subtotal >= 500 ? 0 : 50; // Free shipping above ₹500
      },

      getTax: () => {
        const subtotal = get().getCartSubtotal();
        return Math.round(subtotal * 0.18); // 18% GST
      },

      getFinalTotal: () => {
        const subtotal = get().getCartSubtotal();
        const shipping = get().getShippingCost();
        const tax = get().getTax();
        return subtotal + shipping + tax;
      },

      isInCart: (productId, size = null, color = null) => {
        const { items } = get();
        return items.some(
          item => item.product._id === productId && 
                 item.size === size && 
                 item.color === color
        );
      },

      getCartItem: (productId, size = null, color = null) => {
        const { items } = get();
        return items.find(
          item => item.product._id === productId && 
                 item.size === size && 
                 item.color === color
        );
      },

      // Clear error
      clearError: () => set({ error: null })
    }),
    {
      name: 'cart-storage',
      partialize: (state) => ({
        items: state.items
      })
    }
  )
);

export default useCartStore;
