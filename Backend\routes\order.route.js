const express = require('express');
const { body, validationResult } = require('express-validator');
const Order = require('../models/Order');
const Product = require('../models/Product');
const User = require('../models/User');
const { auth, adminAuth } = require('../middleware/auth');
const { sendEmail } = require('../utils/email');

const router = express.Router();

// Get user's orders
router.get('/my-orders', auth, async (req, res) => {
    try {
        const { page = 1, limit = 10, status } = req.query;

        const filter = { user: req.user.id };
        if (status) filter.status = status;

        const skip = (parseInt(page) - 1) * parseInt(limit);

        const [orders, total] = await Promise.all([
            Order.find(filter)
                .populate('items.product', 'name images slug')
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            Order.countDocuments(filter)
        ]);

        const totalPages = Math.ceil(total / parseInt(limit));

        res.json({
            success: true,
            data: {
                orders,
                pagination: {
                    currentPage: parseInt(page),
                    totalPages,
                    totalOrders: total,
                    hasNextPage: parseInt(page) < totalPages,
                    hasPrevPage: parseInt(page) > 1,
                    limit: parseInt(limit)
                }
            }
        });

    } catch (error) {
        console.error('Get user orders error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch orders',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Get single order
router.get('/:id', auth, async (req, res) => {
    try {
        const { id } = req.params;

        const order = await Order.findById(id)
            .populate('user', 'name email phone')
            .populate('items.product', 'name images slug basePrice')
            .lean();

        if (!order) {
            return res.status(404).json({
                success: false,
                message: 'Order not found'
            });
        }

        // Check if user owns this order or is admin
        if (order.user._id.toString() !== req.user.id && !['admin', 'superadmin'].includes(req.user.role)) {
            return res.status(403).json({
                success: false,
                message: 'Access denied'
            });
        }

        res.json({
            success: true,
            data: { order }
        });

    } catch (error) {
        console.error('Get order error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch order',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Create new order
router.post('/', auth, [
    body('items').isArray({ min: 1 }).withMessage('Items array is required and must not be empty'),
    body('items.*.product').isMongoId().withMessage('Valid product ID is required'),
    body('items.*.quantity').isInt({ min: 1 }).withMessage('Quantity must be at least 1'),
    body('shippingAddress.fullName').trim().isLength({ min: 2 }).withMessage('Full name is required'),
    body('shippingAddress.phone').isMobilePhone('en-IN').withMessage('Valid phone number is required'),
    body('shippingAddress.addressLine1').trim().isLength({ min: 5 }).withMessage('Address line 1 is required'),
    body('shippingAddress.city').trim().isLength({ min: 2 }).withMessage('City is required'),
    body('shippingAddress.state').trim().isLength({ min: 2 }).withMessage('State is required'),
    body('shippingAddress.pincode').isLength({ min: 6, max: 6 }).withMessage('Valid pincode is required'),
    body('paymentMethod').isIn(['razorpay', 'cod']).withMessage('Invalid payment method')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const {
            items,
            shippingAddress,
            billingAddress,
            paymentMethod,
            couponCode,
            notes
        } = req.body;

        // Validate products and calculate totals
        let subtotal = 0;
        const orderItems = [];

        for (const item of items) {
            const product = await Product.findById(item.product);
            if (!product || !product.isActive) {
                return res.status(400).json({
                    success: false,
                    message: `Product ${item.product} not found or inactive`
                });
            }

            // Check stock availability
            if (product.totalStock < item.quantity) {
                return res.status(400).json({
                    success: false,
                    message: `Insufficient stock for ${product.name}. Available: ${product.totalStock}`
                });
            }

            const price = product.salePrice || product.basePrice;
            const itemTotal = price * item.quantity;
            subtotal += itemTotal;

            orderItems.push({
                product: product._id,
                name: product.name,
                image: product.primaryImage,
                price,
                quantity: item.quantity,
                size: item.size,
                color: item.color,
                sku: item.sku
            });
        }

        // Calculate shipping and tax
        const shippingCost = subtotal >= 500 ? 0 : 50; // Free shipping above ₹500
        const tax = Math.round(subtotal * 0.18); // 18% GST
        let discount = 0;

        // Apply coupon if provided
        if (couponCode) {
            // Implement coupon logic here
            // For now, just a simple discount
            if (couponCode === 'WELCOME10') {
                discount = Math.round(subtotal * 0.1); // 10% discount
            }
        }

        const total = subtotal + shippingCost + tax - discount;

        // Create order
        const order = new Order({
            user: req.user.id,
            items: orderItems,
            shippingAddress,
            billingAddress: billingAddress || shippingAddress,
            subtotal,
            shippingCost,
            tax,
            discount,
            couponCode,
            total,
            payment: {
                method: paymentMethod,
                status: paymentMethod === 'cod' ? 'pending' : 'pending'
            },
            notes,
            estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
        });

        await order.save();

        // Update product stock and sold count
        for (const item of items) {
            await Product.findByIdAndUpdate(item.product, {
                $inc: {
                    totalStock: -item.quantity,
                    soldCount: item.quantity
                }
            });
        }

        // Clear user's cart
        await User.findByIdAndUpdate(req.user.id, { cart: [] });

        // Add order to user's orders
        await User.findByIdAndUpdate(req.user.id, {
            $push: { orders: order._id }
        });

        // Send order confirmation email
        try {
            await sendEmail({
                to: req.user.email,
                template: 'orderConfirmation',
                data: {
                    orderNumber: order.orderNumber,
                    orderDate: order.createdAt,
                    total: order.total,
                    paymentMethod: order.payment.method,
                    estimatedDelivery: order.estimatedDelivery.toLocaleDateString(),
                    items: order.items,
                    orderId: order._id
                }
            });
        } catch (emailError) {
            console.error('Failed to send order confirmation email:', emailError);
        }

        // Populate order for response
        await order.populate('items.product', 'name images slug');

        res.status(201).json({
            success: true,
            message: 'Order created successfully',
            data: { order }
        });

    } catch (error) {
        console.error('Create order error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create order',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Update order status (Admin only)
router.patch('/:id/status', adminAuth, [
    body('status').isIn(['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned']).withMessage('Invalid status'),
    body('message').optional().trim(),
    body('location').optional().trim()
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { id } = req.params;
        const { status, message, location } = req.body;

        const order = await Order.findById(id).populate('user', 'name email');
        if (!order) {
            return res.status(404).json({
                success: false,
                message: 'Order not found'
            });
        }

        // Add tracking update
        order.addTracking(status, message, location, req.user.id);
        await order.save();

        // Send email notification for important status changes
        if (['shipped', 'delivered', 'cancelled'].includes(status)) {
            try {
                let template = '';
                if (status === 'shipped') template = 'orderShipped';
                // Add more templates as needed

                if (template) {
                    await sendEmail({
                        to: order.user.email,
                        template,
                        data: {
                            orderNumber: order.orderNumber,
                            trackingNumber: order.trackingNumber,
                            estimatedDelivery: order.estimatedDelivery?.toLocaleDateString(),
                            shippingAddress: `${order.shippingAddress.addressLine1}, ${order.shippingAddress.city}, ${order.shippingAddress.state} - ${order.shippingAddress.pincode}`,
                            orderId: order._id
                        }
                    });
                }
            } catch (emailError) {
                console.error('Failed to send order status email:', emailError);
            }
        }

        res.json({
            success: true,
            message: 'Order status updated successfully',
            data: { order }
        });

    } catch (error) {
        console.error('Update order status error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update order status',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Cancel order
router.patch('/:id/cancel', auth, [
    body('reason').trim().isLength({ min: 5 }).withMessage('Cancellation reason is required')
], async (req, res) => {
    try {
        const { id } = req.params;
        const { reason } = req.body;

        const order = await Order.findById(id);
        if (!order) {
            return res.status(404).json({
                success: false,
                message: 'Order not found'
            });
        }

        // Check if user owns this order
        if (order.user.toString() !== req.user.id) {
            return res.status(403).json({
                success: false,
                message: 'Access denied'
            });
        }

        // Check if order can be cancelled
        if (!order.canCancel) {
            return res.status(400).json({
                success: false,
                message: 'Order cannot be cancelled at this stage'
            });
        }

        // Update order status
        order.status = 'cancelled';
        order.cancellationReason = reason;
        order.cancelledAt = new Date();
        order.addTracking('cancelled', `Order cancelled by customer. Reason: ${reason}`, null, req.user.id);

        await order.save();

        // Restore product stock
        for (const item of order.items) {
            await Product.findByIdAndUpdate(item.product, {
                $inc: {
                    totalStock: item.quantity,
                    soldCount: -item.quantity
                }
            });
        }

        res.json({
            success: true,
            message: 'Order cancelled successfully',
            data: { order }
        });

    } catch (error) {
        console.error('Cancel order error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to cancel order',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

module.exports = router;
