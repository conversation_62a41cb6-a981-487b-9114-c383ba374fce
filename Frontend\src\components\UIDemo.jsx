import React, { useState } from 'react';
import { Input, <PERSON><PERSON><PERSON>, But<PERSON> } from '../ui';

// Simple icons for demonstration
const SearchIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
  </svg>
);

const UserIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
  </svg>
);

const EyeIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
  </svg>
);

const PlusIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
  </svg>
);

const UIDemo = () => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    search: '',
    message: ''
  });

  const handleInputChange = (field) => (e) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value
    }));
  };

  const handleLoadingDemo = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 3000);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            UI Components Demo
          </h1>
          <p className="text-xl text-gray-600">
            Beautiful Input and Button components built with Tailwind CSS
          </p>
        </div>

        {/* Input Components Demo */}
        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Input Components</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Basic Inputs */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-800">Basic Variants</h3>

              <Input
                label="Default Input"
                placeholder="Enter your text..."
                value={formData.email}
                onChange={handleInputChange('email')}
                helperText="This is a default input field"
              />

              <Input
                variant="outlined"
                label="Outlined Input"
                placeholder="Enter your email..."
                type="email"
                helperText="Outlined variant with thicker border"
              />

              <Input
                variant="filled"
                label="Filled Input"
                placeholder="Enter your password..."
                type="password"
                helperText="Filled variant with background"
              />

              <Input
                variant="underlined"
                label="Underlined Input"
                placeholder="Enter your name..."
                helperText="Minimalist underlined style"
              />
            </div>

            {/* Advanced Inputs */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-800">With Icons & States</h3>

              <Input
                label="Search Input"
                placeholder="Search anything..."
                leftIcon={<SearchIcon />}
                value={formData.search}
                onChange={handleInputChange('search')}
              />

              <Input
                label="User Input"
                placeholder="Username"
                leftIcon={<UserIcon />}
                rightIcon={<EyeIcon />}
              />

              <Input
                label="Error State"
                placeholder="This field has an error"
                error="This field is required"
                value=""
              />

              <Input
                label="Disabled Input"
                placeholder="This is disabled"
                disabled
                value="Disabled value"
              />
            </div>
          </div>

          {/* Size Variants */}
          <div className="mt-8">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Size Variants</h3>
            <div className="space-y-4">
              <Input size="sm" placeholder="Small input" />
              <Input size="md" placeholder="Medium input (default)" />
              <Input size="lg" placeholder="Large input" />
            </div>
          </div>

          {/* TextArea Component */}
          <div className="mt-8">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">TextArea Component</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <TextArea
                label="Default TextArea"
                placeholder="Enter your message..."
                helperText="This is a default textarea"
                rows={4}
              />
              <TextArea
                variant="filled"
                label="Filled TextArea"
                placeholder="Enter your feedback..."
                helperText="Filled variant with background"
                rows={4}
              />
              <TextArea
                variant="outlined"
                label="Outlined TextArea"
                placeholder="Enter your comments..."
                helperText="Outlined variant with thicker border"
                rows={3}
              />
              <TextArea
                label="Non-resizable TextArea"
                placeholder="This textarea cannot be resized..."
                resize="none"
                rows={3}
              />
            </div>
          </div>
        </div>

        {/* Button Components Demo */}
        <div className="bg-white rounded-xl shadow-lg p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Button Components</h2>

          {/* Button Variants */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Button Variants</h3>
            <div className="flex flex-wrap gap-4">
              <Button variant="primary">Primary</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="destructive">Destructive</Button>
              <Button variant="success">Success</Button>
              <Button variant="warning">Warning</Button>
            </div>
          </div>

          {/* Button Sizes */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Button Sizes</h3>
            <div className="flex flex-wrap items-center gap-4">
              <Button size="sm">Small</Button>
              <Button size="md">Medium</Button>
              <Button size="lg">Large</Button>
            </div>
          </div>

          {/* Button States */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Button States</h3>
            <div className="flex flex-wrap gap-4">
              <Button>Normal</Button>
              <Button disabled>Disabled</Button>
              <Button loading={loading} onClick={handleLoadingDemo}>
                {loading ? 'Loading...' : 'Click for Loading'}
              </Button>
            </div>
          </div>

          {/* Buttons with Icons */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Buttons with Icons</h3>
            <div className="flex flex-wrap gap-4">
              <Button leftIcon={<PlusIcon />}>Add Item</Button>
              <Button variant="outline" rightIcon={<SearchIcon />}>Search</Button>
              <Button variant="ghost" leftIcon={<UserIcon />} rightIcon={<EyeIcon />}>
                Profile
              </Button>
            </div>
          </div>

          {/* Form Example */}
          <div className="mt-12 p-6 bg-gray-50 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Form Example</h3>
            <div className="space-y-4 max-w-md">
              <Input
                label="Email"
                type="email"
                placeholder="Enter your email"
                leftIcon={<UserIcon />}
                value={formData.email}
                onChange={handleInputChange('email')}
              />
              <Input
                label="Password"
                type="password"
                placeholder="Enter your password"
                rightIcon={<EyeIcon />}
                value={formData.password}
                onChange={handleInputChange('password')}
              />
              <TextArea
                label="Message (Optional)"
                placeholder="Tell us about yourself..."
                rows={3}
                value={formData.message}
                onChange={handleInputChange('message')}
                helperText="This field is optional"
              />
              <div className="flex gap-3 pt-2">
                <Button variant="primary" className="flex-1">
                  Sign In
                </Button>
                <Button variant="outline" className="flex-1">
                  Sign Up
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UIDemo;
