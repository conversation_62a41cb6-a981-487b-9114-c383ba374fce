import { create } from 'zustand';
import { adminAPI, productAPI, categoryAPI, orderAPI, reelAPI } from '../services/api';
import toast from 'react-hot-toast';

const useAdminStore = create((set, get) => ({
  // Dashboard State
  dashboard: {
    data: null,
    isLoading: false,
    error: null,
    lastUpdated: null
  },

  // Users State
  users: {
    data: [],
    pagination: null,
    isLoading: false,
    error: null,
    filters: {}
  },

  // Products State
  products: {
    data: [],
    pagination: null,
    isLoading: false,
    error: null,
    filters: {}
  },

  // Categories State
  categories: {
    data: [],
    tree: [],
    isLoading: false,
    error: null
  },

  // Orders State
  orders: {
    data: [],
    pagination: null,
    isLoading: false,
    error: null,
    filters: {}
  },

  // Reels State
  reels: {
    data: [],
    pagination: null,
    isLoading: false,
    error: null,
    filters: {}
  },

  // Dashboard Actions
  fetchDashboard: async (period = 30) => {
    set((state) => ({
      dashboard: { ...state.dashboard, isLoading: true, error: null }
    }));

    try {
      const response = await adminAPI.getDashboard({ period });
      set((state) => ({
        dashboard: {
          ...state.dashboard,
          data: response.data.data,
          isLoading: false,
          error: null,
          lastUpdated: new Date()
        }
      }));
      return { success: true, data: response.data.data };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch dashboard data';
      set((state) => ({
        dashboard: { ...state.dashboard, isLoading: false, error: errorMessage }
      }));
      return { success: false, error: errorMessage };
    }
  },

  // Users Actions
  fetchUsers: async (params = {}) => {
    set((state) => ({
      users: { ...state.users, isLoading: true, error: null }
    }));

    try {
      const response = await adminAPI.getUsers(params);
      set((state) => ({
        users: {
          ...state.users,
          data: response.data.data.users,
          pagination: response.data.data.pagination,
          isLoading: false,
          error: null,
          filters: params
        }
      }));
      return { success: true, data: response.data.data };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch users';
      set((state) => ({
        users: { ...state.users, isLoading: false, error: errorMessage }
      }));
      return { success: false, error: errorMessage };
    }
  },

  updateUserStatus: async (userId, isActive) => {
    try {
      await adminAPI.updateUserStatus(userId, { isActive });
      
      // Update user in local state
      set((state) => ({
        users: {
          ...state.users,
          data: state.users.data.map(user =>
            user._id === userId ? { ...user, isActive } : user
          )
        }
      }));

      toast.success(`User ${isActive ? 'activated' : 'deactivated'} successfully`);
      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to update user status';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  // Products Actions
  fetchProducts: async (params = {}) => {
    set((state) => ({
      products: { ...state.products, isLoading: true, error: null }
    }));

    try {
      const response = await productAPI.getProducts(params);
      set((state) => ({
        products: {
          ...state.products,
          data: response.data.data.products,
          pagination: response.data.data.pagination,
          isLoading: false,
          error: null,
          filters: params
        }
      }));
      return { success: true, data: response.data.data };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch products';
      set((state) => ({
        products: { ...state.products, isLoading: false, error: errorMessage }
      }));
      return { success: false, error: errorMessage };
    }
  },

  createProduct: async (productData) => {
    try {
      const response = await productAPI.createProduct(productData);
      
      // Add product to local state
      set((state) => ({
        products: {
          ...state.products,
          data: [response.data.data.product, ...state.products.data]
        }
      }));

      toast.success('Product created successfully');
      return { success: true, data: response.data.data.product };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to create product';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  updateProduct: async (productId, productData) => {
    try {
      const response = await productAPI.updateProduct(productId, productData);
      
      // Update product in local state
      set((state) => ({
        products: {
          ...state.products,
          data: state.products.data.map(product =>
            product._id === productId ? response.data.data.product : product
          )
        }
      }));

      toast.success('Product updated successfully');
      return { success: true, data: response.data.data.product };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to update product';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  deleteProduct: async (productId) => {
    try {
      await productAPI.deleteProduct(productId);
      
      // Remove product from local state
      set((state) => ({
        products: {
          ...state.products,
          data: state.products.data.filter(product => product._id !== productId)
        }
      }));

      toast.success('Product deleted successfully');
      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to delete product';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  // Categories Actions
  fetchCategories: async () => {
    set((state) => ({
      categories: { ...state.categories, isLoading: true, error: null }
    }));

    try {
      const [categoriesResponse, treeResponse] = await Promise.all([
        categoryAPI.getCategories(),
        categoryAPI.getCategoryTree()
      ]);

      set((state) => ({
        categories: {
          ...state.categories,
          data: categoriesResponse.data.data.categories,
          tree: treeResponse.data.data.categories,
          isLoading: false,
          error: null
        }
      }));

      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch categories';
      set((state) => ({
        categories: { ...state.categories, isLoading: false, error: errorMessage }
      }));
      return { success: false, error: errorMessage };
    }
  },

  createCategory: async (categoryData) => {
    try {
      const response = await categoryAPI.createCategory(categoryData);
      
      // Refresh categories to update tree structure
      get().fetchCategories();

      toast.success('Category created successfully');
      return { success: true, data: response.data.data.category };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to create category';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  updateCategory: async (categoryId, categoryData) => {
    try {
      const response = await categoryAPI.updateCategory(categoryId, categoryData);
      
      // Refresh categories to update tree structure
      get().fetchCategories();

      toast.success('Category updated successfully');
      return { success: true, data: response.data.data.category };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to update category';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  deleteCategory: async (categoryId) => {
    try {
      await categoryAPI.deleteCategory(categoryId);
      
      // Refresh categories to update tree structure
      get().fetchCategories();

      toast.success('Category deleted successfully');
      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to delete category';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  // Orders Actions
  fetchOrders: async (params = {}) => {
    set((state) => ({
      orders: { ...state.orders, isLoading: true, error: null }
    }));

    try {
      const response = await adminAPI.getOrders(params);
      set((state) => ({
        orders: {
          ...state.orders,
          data: response.data.data.orders,
          pagination: response.data.data.pagination,
          isLoading: false,
          error: null,
          filters: params
        }
      }));
      return { success: true, data: response.data.data };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch orders';
      set((state) => ({
        orders: { ...state.orders, isLoading: false, error: errorMessage }
      }));
      return { success: false, error: errorMessage };
    }
  },

  updateOrderStatus: async (orderId, statusData) => {
    try {
      const response = await orderAPI.updateOrderStatus(orderId, statusData);
      
      // Update order in local state
      set((state) => ({
        orders: {
          ...state.orders,
          data: state.orders.data.map(order =>
            order._id === orderId ? response.data.data.order : order
          )
        }
      }));

      toast.success('Order status updated successfully');
      return { success: true, data: response.data.data.order };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to update order status';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  // Reels Actions
  fetchReels: async (params = {}) => {
    set((state) => ({
      reels: { ...state.reels, isLoading: true, error: null }
    }));

    try {
      const response = await reelAPI.getReels(params);
      set((state) => ({
        reels: {
          ...state.reels,
          data: response.data.data.reels,
          pagination: response.data.data.pagination,
          isLoading: false,
          error: null,
          filters: params
        }
      }));
      return { success: true, data: response.data.data };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch reels';
      set((state) => ({
        reels: { ...state.reels, isLoading: false, error: errorMessage }
      }));
      return { success: false, error: errorMessage };
    }
  },

  createReel: async (reelData) => {
    try {
      const response = await reelAPI.createReel(reelData);
      
      // Add reel to local state
      set((state) => ({
        reels: {
          ...state.reels,
          data: [response.data.data.reel, ...state.reels.data]
        }
      }));

      toast.success('Reel created successfully');
      return { success: true, data: response.data.data.reel };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to create reel';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  // Clear functions
  clearDashboard: () => {
    set((state) => ({
      dashboard: { ...state.dashboard, data: null, error: null }
    }));
  },

  clearUsers: () => {
    set((state) => ({
      users: { ...state.users, data: [], pagination: null, error: null }
    }));
  },

  clearProducts: () => {
    set((state) => ({
      products: { ...state.products, data: [], pagination: null, error: null }
    }));
  },

  clearOrders: () => {
    set((state) => ({
      orders: { ...state.orders, data: [], pagination: null, error: null }
    }));
  },

  clearReels: () => {
    set((state) => ({
      reels: { ...state.reels, data: [], pagination: null, error: null }
    }));
  }
}));

export default useAdminStore;
