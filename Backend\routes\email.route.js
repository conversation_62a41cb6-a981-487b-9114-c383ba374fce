const express = require('express');
const { body, validationResult } = require('express-validator');
const { sendEmail, sendBulkEmail } = require('../utils/email');
const { adminAuth } = require('../middleware/auth');
const User = require('../models/User');

const router = express.Router();

// Send newsletter to all subscribers
router.post('/newsletter', adminAuth, [
    body('subject').trim().isLength({ min: 5 }).withMessage('Subject must be at least 5 characters'),
    body('content').trim().isLength({ min: 10 }).withMessage('Content must be at least 10 characters'),
    body('template').optional().isIn(['newsletter', 'promotion', 'announcement']).withMessage('Invalid template')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { subject, content, template = 'newsletter', includeInactive = false } = req.body;

        // Get all users who have opted in for newsletters
        const filter = { 'preferences.newsletter': true };
        if (!includeInactive) filter.isActive = true;

        const subscribers = await User.find(filter).select('email name').lean();

        if (subscribers.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No subscribers found'
            });
        }

        // Prepare emails
        const emails = subscribers.map(subscriber => ({
            to: subscriber.email,
            subject,
            html: generateNewsletterHTML(content, subscriber.name, template)
        }));

        // Send bulk emails
        const result = await sendBulkEmail(emails);

        res.json({
            success: true,
            message: 'Newsletter sent successfully',
            data: {
                totalSubscribers: subscribers.length,
                successful: result.successful,
                failed: result.failed
            }
        });

    } catch (error) {
        console.error('Send newsletter error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to send newsletter',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Send promotional email
router.post('/promotion', adminAuth, [
    body('subject').trim().isLength({ min: 5 }).withMessage('Subject must be at least 5 characters'),
    body('content').trim().isLength({ min: 10 }).withMessage('Content must be at least 10 characters'),
    body('couponCode').optional().trim(),
    body('discountPercentage').optional().isFloat({ min: 0, max: 100 }).withMessage('Discount must be between 0 and 100'),
    body('validUntil').optional().isISO8601().withMessage('Valid until must be a valid date')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { 
            subject, 
            content, 
            couponCode, 
            discountPercentage, 
            validUntil,
            targetSegment = 'all' 
        } = req.body;

        // Build user filter based on target segment
        let filter = { 'preferences.newsletter': true, isActive: true };
        
        switch (targetSegment) {
            case 'new_users':
                filter.createdAt = { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }; // Last 30 days
                break;
            case 'active_users':
                filter.lastLogin = { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }; // Last 7 days
                break;
            case 'inactive_users':
                filter.lastLogin = { $lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }; // More than 30 days ago
                break;
            // 'all' uses default filter
        }

        const users = await User.find(filter).select('email name').lean();

        if (users.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No users found for the selected segment'
            });
        }

        // Prepare promotional emails
        const emails = users.map(user => ({
            to: user.email,
            subject,
            html: generatePromotionalHTML({
                content,
                userName: user.name,
                couponCode,
                discountPercentage,
                validUntil
            })
        }));

        // Send bulk emails
        const result = await sendBulkEmail(emails);

        res.json({
            success: true,
            message: 'Promotional email sent successfully',
            data: {
                targetSegment,
                totalUsers: users.length,
                successful: result.successful,
                failed: result.failed
            }
        });

    } catch (error) {
        console.error('Send promotional email error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to send promotional email',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Send custom email to specific users
router.post('/custom', adminAuth, [
    body('recipients').isArray({ min: 1 }).withMessage('Recipients array is required'),
    body('recipients.*').isEmail().withMessage('All recipients must be valid email addresses'),
    body('subject').trim().isLength({ min: 5 }).withMessage('Subject must be at least 5 characters'),
    body('content').trim().isLength({ min: 10 }).withMessage('Content must be at least 10 characters')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { recipients, subject, content, isHTML = true } = req.body;

        // Prepare emails
        const emails = recipients.map(email => ({
            to: email,
            subject,
            [isHTML ? 'html' : 'text']: content
        }));

        // Send bulk emails
        const result = await sendBulkEmail(emails);

        res.json({
            success: true,
            message: 'Custom emails sent successfully',
            data: {
                totalRecipients: recipients.length,
                successful: result.successful,
                failed: result.failed
            }
        });

    } catch (error) {
        console.error('Send custom email error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to send custom emails',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Test email functionality
router.post('/test', adminAuth, [
    body('to').isEmail().withMessage('Valid email address is required'),
    body('template').optional().isIn(['welcome', 'orderConfirmation', 'passwordReset', 'orderShipped']).withMessage('Invalid template')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { to, template = 'welcome' } = req.body;

        // Mock data for testing
        const testData = {
            welcome: {
                name: 'Test User',
                verificationUrl: `${process.env.FRONTEND_URL}/verify-email?token=test-token`
            },
            orderConfirmation: {
                orderNumber: 'NF123456789',
                orderDate: new Date(),
                total: 1299,
                paymentMethod: 'Razorpay',
                estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString(),
                items: [
                    { name: 'Test Product', quantity: 1, price: 1299 }
                ],
                orderId: 'test-order-id'
            },
            passwordReset: {
                name: 'Test User',
                resetUrl: `${process.env.FRONTEND_URL}/reset-password?token=test-token`
            },
            orderShipped: {
                orderNumber: 'NF123456789',
                trackingNumber: 'TRK123456789',
                estimatedDelivery: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toLocaleDateString(),
                shippingAddress: 'Test Address, Test City, Test State - 123456',
                orderId: 'test-order-id'
            }
        };

        await sendEmail({
            to,
            template,
            data: testData[template]
        });

        res.json({
            success: true,
            message: `Test email sent successfully to ${to}`,
            data: { template, testData: testData[template] }
        });

    } catch (error) {
        console.error('Send test email error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to send test email',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Helper function to generate newsletter HTML
function generateNewsletterHTML(content, userName, template) {
    return `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #e91e63; margin: 0;">Nagma Fashion</h1>
                <p style="color: #666; margin: 5px 0;">Your Style, Our Passion</p>
            </div>
            
            <div style="background: linear-gradient(135deg, #e91e63, #9c27b0); padding: 20px; border-radius: 10px; color: white; text-align: center; margin-bottom: 30px;">
                <h2 style="margin: 0;">Hi ${userName}! 👋</h2>
            </div>
            
            <div style="padding: 20px; background: #f9f9f9; border-radius: 10px; margin-bottom: 30px;">
                ${content}
            </div>
            
            <div style="text-align: center; margin-bottom: 30px;">
                <a href="${process.env.FRONTEND_URL}" style="background: #e91e63; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; display: inline-block;">
                    Shop Now
                </a>
            </div>
            
            <div style="text-align: center; color: #666; font-size: 14px;">
                <p>© 2024 Nagma Fashion. All rights reserved.</p>
                <p><a href="${process.env.FRONTEND_URL}/unsubscribe" style="color: #666;">Unsubscribe</a></p>
            </div>
        </div>
    `;
}

// Helper function to generate promotional HTML
function generatePromotionalHTML({ content, userName, couponCode, discountPercentage, validUntil }) {
    return `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #e91e63; margin: 0;">Nagma Fashion</h1>
            </div>
            
            <div style="background: #ff9800; padding: 20px; border-radius: 10px; color: white; text-align: center; margin-bottom: 30px;">
                <h2 style="margin: 0;">Special Offer for ${userName}! 🎉</h2>
                ${discountPercentage ? `<p style="margin: 10px 0; font-size: 18px;">${discountPercentage}% OFF</p>` : ''}
            </div>
            
            <div style="padding: 20px; background: #f9f9f9; border-radius: 10px; margin-bottom: 30px;">
                ${content}
            </div>
            
            ${couponCode ? `
                <div style="text-align: center; margin-bottom: 30px; padding: 20px; border: 2px dashed #e91e63; border-radius: 10px;">
                    <h3 style="margin: 0; color: #e91e63;">Use Coupon Code</h3>
                    <p style="font-size: 24px; font-weight: bold; margin: 10px 0; color: #333;">${couponCode}</p>
                    ${validUntil ? `<p style="color: #666; margin: 0;">Valid until: ${new Date(validUntil).toLocaleDateString()}</p>` : ''}
                </div>
            ` : ''}
            
            <div style="text-align: center; margin-bottom: 30px;">
                <a href="${process.env.FRONTEND_URL}" style="background: #e91e63; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; display: inline-block;">
                    Shop Now
                </a>
            </div>
            
            <div style="text-align: center; color: #666; font-size: 14px;">
                <p>© 2024 Nagma Fashion. All rights reserved.</p>
                <p><a href="${process.env.FRONTEND_URL}/unsubscribe" style="color: #666;">Unsubscribe</a></p>
            </div>
        </div>
    `;
}

module.exports = router;
