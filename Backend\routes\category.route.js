const express = require('express');
const { body, validationResult } = require('express-validator');
const Category = require('../models/Category');
const Product = require('../models/Product');
const { adminAuth, optionalAuth } = require('../middleware/auth');
const { uploadCategory } = require('../config/cloudinary');

const router = express.Router();

// Get all categories
router.get('/', optionalAuth, async (req, res) => {
    try {
        const { includeInactive = false, parentOnly = false } = req.query;

        const filter = {};
        if (!includeInactive) filter.isActive = true;
        if (parentOnly === 'true') filter.parent = null;

        const categories = await Category.find(filter)
            .populate('parent', 'name slug')
            .populate('children', 'name slug')
            .sort({ sortOrder: 1, name: 1 })
            .lean();

        // Get product counts for each category
        const categoriesWithCounts = await Promise.all(
            categories.map(async (category) => {
                const productCount = await Product.countDocuments({
                    category: category._id,
                    isActive: true
                });
                return { ...category, productCount };
            })
        );

        res.json({
            success: true,
            data: { categories: categoriesWithCounts }
        });

    } catch (error) {
        console.error('Get categories error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch categories',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Get category tree structure
router.get('/tree', optionalAuth, async (req, res) => {
    try {
        const categories = await Category.find({ isActive: true })
            .sort({ sortOrder: 1, name: 1 })
            .lean();

        // Build tree structure
        const buildTree = (parentId = null) => {
            return categories
                .filter(cat => (cat.parent ? cat.parent.toString() : null) === parentId)
                .map(cat => ({
                    ...cat,
                    children: buildTree(cat._id.toString())
                }));
        };

        const tree = buildTree();

        res.json({
            success: true,
            data: { categories: tree }
        });

    } catch (error) {
        console.error('Get category tree error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch category tree',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Get single category
router.get('/:identifier', optionalAuth, async (req, res) => {
    try {
        const { identifier } = req.params;
        
        // Check if identifier is ObjectId or slug
        const isObjectId = /^[0-9a-fA-F]{24}$/.test(identifier);
        const filter = isObjectId ? { _id: identifier } : { slug: identifier };
        filter.isActive = true;

        const category = await Category.findOne(filter)
            .populate('parent', 'name slug')
            .populate('children', 'name slug image')
            .lean();

        if (!category) {
            return res.status(404).json({
                success: false,
                message: 'Category not found'
            });
        }

        // Get product count
        const productCount = await Product.countDocuments({
            category: category._id,
            isActive: true
        });

        // Get featured products from this category
        const featuredProducts = await Product.find({
            category: category._id,
            isActive: true,
            isFeatured: true
        })
        .limit(8)
        .select('name slug images basePrice salePrice rating')
        .lean();

        res.json({
            success: true,
            data: {
                category: { ...category, productCount },
                featuredProducts
            }
        });

    } catch (error) {
        console.error('Get category error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch category',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Create new category (Admin only)
router.post('/', adminAuth, uploadCategory.single('image'), [
    body('name').trim().isLength({ min: 2 }).withMessage('Category name must be at least 2 characters'),
    body('description').optional().trim(),
    body('parent').optional().isMongoId().withMessage('Parent must be a valid category ID'),
    body('sortOrder').optional().isInt({ min: 0 }).withMessage('Sort order must be a non-negative integer'),
    body('icon').optional().trim(),
    body('isFeatured').optional().isBoolean().withMessage('isFeatured must be a boolean')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const {
            name,
            description,
            parent,
            sortOrder,
            icon,
            isFeatured,
            seo
        } = req.body;

        // Check if parent category exists
        if (parent) {
            const parentCategory = await Category.findById(parent);
            if (!parentCategory) {
                return res.status(400).json({
                    success: false,
                    message: 'Parent category not found'
                });
            }
        }

        // Process uploaded image
        const image = req.file ? {
            url: req.file.path,
            alt: `${name} category image`
        } : undefined;

        // Calculate level based on parent
        let level = 0;
        if (parent) {
            const parentCategory = await Category.findById(parent);
            level = parentCategory.level + 1;
        }

        // Create category
        const category = new Category({
            name,
            description,
            image,
            icon,
            parent: parent || null,
            level,
            sortOrder: sortOrder || 0,
            isFeatured: isFeatured || false,
            seo: seo ? JSON.parse(seo) : {},
            createdBy: req.user.id
        });

        await category.save();

        // Populate parent for response
        await category.populate('parent', 'name slug');

        res.status(201).json({
            success: true,
            message: 'Category created successfully',
            data: { category }
        });

    } catch (error) {
        console.error('Create category error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create category',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Update category (Admin only)
router.put('/:id', adminAuth, uploadCategory.single('image'), async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = { ...req.body };

        // Parse JSON fields if they exist
        if (updateData.seo) updateData.seo = JSON.parse(updateData.seo);

        // Process new image if uploaded
        if (req.file) {
            updateData.image = {
                url: req.file.path,
                alt: `${updateData.name || 'Category'} image`
            };
        }

        // Calculate level if parent changed
        if (updateData.parent) {
            const parentCategory = await Category.findById(updateData.parent);
            if (!parentCategory) {
                return res.status(400).json({
                    success: false,
                    message: 'Parent category not found'
                });
            }
            updateData.level = parentCategory.level + 1;
        } else if (updateData.parent === null) {
            updateData.level = 0;
        }

        updateData.updatedBy = req.user.id;

        const category = await Category.findByIdAndUpdate(id, updateData, {
            new: true,
            runValidators: true
        }).populate('parent', 'name slug');

        if (!category) {
            return res.status(404).json({
                success: false,
                message: 'Category not found'
            });
        }

        res.json({
            success: true,
            message: 'Category updated successfully',
            data: { category }
        });

    } catch (error) {
        console.error('Update category error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update category',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Delete category (Admin only)
router.delete('/:id', adminAuth, async (req, res) => {
    try {
        const { id } = req.params;

        // Check if category has products
        const productCount = await Product.countDocuments({ category: id });
        if (productCount > 0) {
            return res.status(400).json({
                success: false,
                message: `Cannot delete category. It has ${productCount} products associated with it.`
            });
        }

        // Check if category has children
        const childrenCount = await Category.countDocuments({ parent: id });
        if (childrenCount > 0) {
            return res.status(400).json({
                success: false,
                message: `Cannot delete category. It has ${childrenCount} subcategories.`
            });
        }

        const category = await Category.findByIdAndDelete(id);
        if (!category) {
            return res.status(404).json({
                success: false,
                message: 'Category not found'
            });
        }

        res.json({
            success: true,
            message: 'Category deleted successfully'
        });

    } catch (error) {
        console.error('Delete category error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete category',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

module.exports = router;
