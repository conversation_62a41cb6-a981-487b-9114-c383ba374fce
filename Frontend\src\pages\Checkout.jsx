import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import { CreditCardIcon, TruckIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';
import useCartStore from '../stores/cartStore';
import useAuthStore from '../stores/authStore';
import { orderAPI, paymentAPI } from '../services/api';
import { Button, Input } from '../ui';
import toast from 'react-hot-toast';

const Checkout = () => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('razorpay');
  const navigate = useNavigate();

  const { items, getCartSubtotal, getShippingCost, getTax, getFinalTotal, clearCart } = useCartStore();
  const { user, isAuthenticated } = useAuthStore();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue
  } = useForm();

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { state: { from: { pathname: '/checkout' } } });
      return;
    }

    if (items.length === 0) {
      navigate('/');
      toast.error('Your cart is empty');
      return;
    }

    // Pre-fill form with user data
    if (user) {
      setValue('email', user.email);
      setValue('name', user.name);
      setValue('phone', user.phone || '');
      
      // Pre-fill with default address if available
      const defaultAddress = user.addresses?.find(addr => addr.isDefault);
      if (defaultAddress) {
        setValue('fullName', defaultAddress.fullName);
        setValue('phone', defaultAddress.phone);
        setValue('addressLine1', defaultAddress.addressLine1);
        setValue('addressLine2', defaultAddress.addressLine2);
        setValue('city', defaultAddress.city);
        setValue('state', defaultAddress.state);
        setValue('pincode', defaultAddress.pincode);
      }
    }
  }, [isAuthenticated, items.length, user, navigate, setValue]);

  const subtotal = getCartSubtotal();
  const shipping = getShippingCost();
  const tax = getTax();
  const total = getFinalTotal();

  const loadRazorpayScript = () => {
    return new Promise((resolve) => {
      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => resolve(true);
      script.onerror = () => resolve(false);
      document.body.appendChild(script);
    });
  };

  const handleRazorpayPayment = async (orderData, razorpayOrder) => {
    const isScriptLoaded = await loadRazorpayScript();
    
    if (!isScriptLoaded) {
      toast.error('Failed to load payment gateway');
      return;
    }

    const options = {
      key: import.meta.env.VITE_RAZORPAY_KEY_ID,
      amount: razorpayOrder.amount,
      currency: razorpayOrder.currency,
      name: 'Nagma Fashion',
      description: `Order #${orderData.orderNumber}`,
      order_id: razorpayOrder.razorpayOrderId,
      prefill: {
        name: orderData.shippingAddress.fullName,
        email: user.email,
        contact: orderData.shippingAddress.phone
      },
      theme: {
        color: '#ec4899'
      },
      handler: async (response) => {
        try {
          setIsProcessing(true);
          
          const verifyResult = await paymentAPI.verifyPayment({
            razorpay_order_id: response.razorpay_order_id,
            razorpay_payment_id: response.razorpay_payment_id,
            razorpay_signature: response.razorpay_signature,
            orderId: orderData._id
          });

          if (verifyResult.data.success) {
            await clearCart();
            toast.success('Payment successful! Order placed.');
            navigate(`/orders/${orderData._id}`);
          }
        } catch (error) {
          console.error('Payment verification failed:', error);
          toast.error('Payment verification failed');
        } finally {
          setIsProcessing(false);
        }
      },
      modal: {
        ondismiss: () => {
          setIsProcessing(false);
          toast.error('Payment cancelled');
        }
      }
    };

    const razorpay = new window.Razorpay(options);
    razorpay.open();
  };

  const onSubmit = async (data) => {
    setIsProcessing(true);

    try {
      // Create order
      const orderData = {
        items: items.map(item => ({
          product: item.product._id,
          quantity: item.quantity,
          size: item.size,
          color: item.color
        })),
        shippingAddress: {
          fullName: data.fullName,
          phone: data.phone,
          addressLine1: data.addressLine1,
          addressLine2: data.addressLine2,
          city: data.city,
          state: data.state,
          pincode: data.pincode,
          country: 'India'
        },
        paymentMethod,
        notes: data.notes
      };

      const orderResponse = await orderAPI.createOrder(orderData);
      const order = orderResponse.data.data.order;

      if (paymentMethod === 'cod') {
        // Cash on Delivery
        await clearCart();
        toast.success('Order placed successfully!');
        navigate(`/orders/${order._id}`);
      } else {
        // Razorpay Payment
        const paymentResponse = await paymentAPI.createOrder({
          orderId: order._id,
          amount: total
        });

        await handleRazorpayPayment(order, paymentResponse.data.data);
      }
    } catch (error) {
      console.error('Checkout error:', error);
      toast.error(error.response?.data?.message || 'Failed to place order');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-2xl mx-auto lg:max-w-none lg:grid lg:grid-cols-2 lg:gap-12">
          {/* Order Summary */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-white rounded-lg shadow p-6 lg:order-2"
          >
            <h2 className="text-lg font-medium text-gray-900 mb-6">Order Summary</h2>
            
            <div className="space-y-4">
              {items.map((item) => (
                <div key={item._id} className="flex space-x-4">
                  <img
                    src={item.product.images?.[0]?.url || '/placeholder-image.jpg'}
                    alt={item.product.name}
                    className="w-16 h-16 object-cover rounded-md"
                  />
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-gray-900">{item.product.name}</h3>
                    <p className="text-sm text-gray-500">
                      {item.size && `Size: ${item.size}`}
                      {item.size && item.color && ' • '}
                      {item.color && `Color: ${item.color}`}
                    </p>
                    <p className="text-sm text-gray-500">Qty: {item.quantity}</p>
                  </div>
                  <p className="text-sm font-medium text-gray-900">
                    ₹{(item.product.salePrice || item.product.basePrice) * item.quantity}
                  </p>
                </div>
              ))}
            </div>

            <div className="border-t border-gray-200 pt-4 mt-6 space-y-2">
              <div className="flex justify-between text-sm">
                <span>Subtotal</span>
                <span>₹{subtotal}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Shipping</span>
                <span>{shipping === 0 ? 'Free' : `₹${shipping}`}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Tax (GST)</span>
                <span>₹{tax}</span>
              </div>
              <div className="border-t border-gray-200 pt-2">
                <div className="flex justify-between text-base font-medium">
                  <span>Total</span>
                  <span>₹{total}</span>
                </div>
              </div>
            </div>

            {/* Security badges */}
            <div className="mt-6 flex items-center justify-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center">
                <ShieldCheckIcon className="h-5 w-5 mr-1" />
                <span>Secure</span>
              </div>
              <div className="flex items-center">
                <TruckIcon className="h-5 w-5 mr-1" />
                <span>Fast Delivery</span>
              </div>
            </div>
          </motion.div>

          {/* Checkout Form */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-white rounded-lg shadow p-6"
          >
            <h1 className="text-2xl font-bold text-gray-900 mb-6">Checkout</h1>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Contact Information */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Full Name"
                    {...register('fullName', { required: 'Full name is required' })}
                    error={errors.fullName?.message}
                  />
                  <Input
                    label="Phone Number"
                    type="tel"
                    {...register('phone', {
                      required: 'Phone number is required',
                      pattern: {
                        value: /^[6-9]\d{9}$/,
                        message: 'Invalid phone number'
                      }
                    })}
                    error={errors.phone?.message}
                  />
                </div>
              </div>

              {/* Shipping Address */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Shipping Address</h3>
                <div className="space-y-4">
                  <Input
                    label="Address Line 1"
                    {...register('addressLine1', { required: 'Address is required' })}
                    error={errors.addressLine1?.message}
                  />
                  <Input
                    label="Address Line 2 (Optional)"
                    {...register('addressLine2')}
                  />
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Input
                      label="City"
                      {...register('city', { required: 'City is required' })}
                      error={errors.city?.message}
                    />
                    <Input
                      label="State"
                      {...register('state', { required: 'State is required' })}
                      error={errors.state?.message}
                    />
                    <Input
                      label="Pincode"
                      {...register('pincode', {
                        required: 'Pincode is required',
                        pattern: {
                          value: /^\d{6}$/,
                          message: 'Invalid pincode'
                        }
                      })}
                      error={errors.pincode?.message}
                    />
                  </div>
                </div>
              </div>

              {/* Payment Method */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Method</h3>
                <div className="space-y-3">
                  <label className="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="radio"
                      value="razorpay"
                      checked={paymentMethod === 'razorpay'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                      className="text-pink-600 focus:ring-pink-500"
                    />
                    <CreditCardIcon className="h-6 w-6 ml-3 mr-3 text-gray-400" />
                    <div>
                      <p className="font-medium">Credit/Debit Card, UPI, Net Banking</p>
                      <p className="text-sm text-gray-500">Secure payment via Razorpay</p>
                    </div>
                  </label>
                  <label className="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="radio"
                      value="cod"
                      checked={paymentMethod === 'cod'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                      className="text-pink-600 focus:ring-pink-500"
                    />
                    <TruckIcon className="h-6 w-6 ml-3 mr-3 text-gray-400" />
                    <div>
                      <p className="font-medium">Cash on Delivery</p>
                      <p className="text-sm text-gray-500">Pay when you receive your order</p>
                    </div>
                  </label>
                </div>
              </div>

              {/* Order Notes */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Order Notes (Optional)
                </label>
                <textarea
                  {...register('notes')}
                  rows={3}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-pink-500 focus:ring-pink-500"
                  placeholder="Any special instructions for your order..."
                />
              </div>

              {/* Place Order Button */}
              <Button
                type="submit"
                className="w-full"
                size="lg"
                isLoading={isProcessing}
                disabled={isProcessing}
              >
                {isProcessing
                  ? 'Processing...'
                  : paymentMethod === 'cod'
                  ? 'Place Order'
                  : `Pay ₹${total}`
                }
              </Button>
            </form>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Checkout;
