import axios from 'axios';
import toast from 'react-hot-toast';

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const message = error.response?.data?.message || error.message || 'Something went wrong';
    
    // Handle specific error codes
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
      toast.error('Session expired. Please login again.');
    } else if (error.response?.status === 403) {
      toast.error('Access denied. Insufficient permissions.');
    } else if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.');
    } else {
      toast.error(message);
    }
    
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  register: (data) => api.post('/auth/register', data),
  login: (data) => api.post('/auth/login', data),
  logout: () => api.post('/auth/logout'),
  getMe: () => api.get('/auth/me'),
  verifyEmail: (token) => api.post('/auth/verify-email', { token }),
  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
  resetPassword: (data) => api.post('/auth/reset-password', data),
  googleAuth: () => `${api.defaults.baseURL}/auth/google`,
};

// User API
export const userAPI = {
  getProfile: () => api.get('/users/profile'),
  updateProfile: (data) => api.put('/users/profile', data),
  addToCart: (data) => api.post('/users/cart', data),
  updateCartItem: (itemId, data) => api.put(`/users/cart/${itemId}`, data),
  removeFromCart: (itemId) => api.delete(`/users/cart/${itemId}`),
  clearCart: () => api.delete('/users/cart'),
  addToWishlist: (productId) => api.post('/users/wishlist', { productId }),
  removeFromWishlist: (productId) => api.delete(`/users/wishlist/${productId}`),
  addAddress: (data) => api.post('/users/addresses', data),
  updateAddress: (addressId, data) => api.put(`/users/addresses/${addressId}`, data),
  deleteAddress: (addressId) => api.delete(`/users/addresses/${addressId}`),
};

// Product API
export const productAPI = {
  getProducts: (params) => api.get('/products', { params }),
  getProduct: (id) => api.get(`/products/${id}`),
  createProduct: (data) => api.post('/products', data),
  updateProduct: (id, data) => api.put(`/products/${id}`, data),
  deleteProduct: (id) => api.delete(`/products/${id}`),
  addReview: (id, data) => api.post(`/products/${id}/reviews`, data),
};

// Category API
export const categoryAPI = {
  getCategories: (params) => api.get('/categories', { params }),
  getCategoryTree: () => api.get('/categories/tree'),
  getCategory: (id) => api.get(`/categories/${id}`),
  createCategory: (data) => api.post('/categories', data),
  updateCategory: (id, data) => api.put(`/categories/${id}`, data),
  deleteCategory: (id) => api.delete(`/categories/${id}`),
};

// Order API
export const orderAPI = {
  getMyOrders: (params) => api.get('/orders/my-orders', { params }),
  getOrder: (id) => api.get(`/orders/${id}`),
  createOrder: (data) => api.post('/orders', data),
  updateOrderStatus: (id, data) => api.patch(`/orders/${id}/status`, data),
  cancelOrder: (id, data) => api.patch(`/orders/${id}/cancel`, data),
};

// Admin API
export const adminAPI = {
  getDashboard: (params) => api.get('/admin/dashboard', { params }),
  getUsers: (params) => api.get('/admin/users', { params }),
  updateUserStatus: (id, data) => api.patch(`/admin/users/${id}/status`, data),
  getOrders: (params) => api.get('/admin/orders', { params }),
};

// Payment API
export const paymentAPI = {
  createOrder: (data) => api.post('/payment/create-order', data),
  verifyPayment: (data) => api.post('/payment/verify-payment', data),
  paymentFailed: (data) => api.post('/payment/payment-failed', data),
  getPaymentStatus: (orderId) => api.get(`/payment/status/${orderId}`),
};

// Reel API
export const reelAPI = {
  getReels: (params) => api.get('/reels', { params }),
  getReel: (id) => api.get(`/reels/${id}`),
  createReel: (data) => api.post('/reels', data),
  likeReel: (id) => api.post(`/reels/${id}/like`),
  saveReel: (id) => api.post(`/reels/${id}/save`),
  addComment: (id, data) => api.post(`/reels/${id}/comments`, data),
  shareReel: (id) => api.post(`/reels/${id}/share`),
};

// Upload API
export const uploadAPI = {
  uploadProducts: (files) => {
    const formData = new FormData();
    files.forEach(file => formData.append('images', file));
    return api.post('/upload/products', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },
  uploadReels: (file) => {
    const formData = new FormData();
    formData.append('video', file);
    return api.post('/upload/reels', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },
  uploadCategories: (file) => {
    const formData = new FormData();
    formData.append('image', file);
    return api.post('/upload/categories', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },
  uploadAvatar: (file) => {
    const formData = new FormData();
    formData.append('avatar', file);
    return api.post('/upload/avatar', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },
  uploadBanners: (files) => {
    const formData = new FormData();
    files.forEach(file => formData.append('banners', file));
    return api.post('/upload/banners', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },
  deleteFile: (publicId, resourceType = 'image') => 
    api.delete(`/upload/${publicId}`, { params: { resourceType } }),
};

// Email API
export const emailAPI = {
  sendNewsletter: (data) => api.post('/email/newsletter', data),
  sendPromotion: (data) => api.post('/email/promotion', data),
  sendCustomEmail: (data) => api.post('/email/custom', data),
  sendTestEmail: (data) => api.post('/email/test', data),
};

// Utility functions
export const setAuthToken = (token) => {
  if (token) {
    localStorage.setItem('token', token);
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  } else {
    localStorage.removeItem('token');
    delete api.defaults.headers.common['Authorization'];
  }
};

export const getAuthToken = () => {
  return localStorage.getItem('token');
};

export const isAuthenticated = () => {
  return !!getAuthToken();
};

export default api;
