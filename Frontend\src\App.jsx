import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, useTheme } from './context/ThemeContext';

// Pages
import HomePage from './components/pages/Home/HomePage';
import ShopPage from './components/pages/Shop/ShopPage';
import LoginPage from './components/pages/Auth/LoginPage';
import SignupPage from './components/pages/Auth/SignupPage';
import CartPage from './components/pages/Cart/CartPage';
import CheckoutPage from './components/pages/Checkout/CheckoutPage';
import ProductDetailPage from './components/pages/Product/ProductDetailPage';
import NewArrivalsPage from './components/pages/NewArrivals/NewArrivalsPage';
import SalePage from './components/pages/Sale/SalePage';
import AboutPage from './components/pages/About/AboutPage';
import ContactPage from './components/pages/Contact/ContactPage';
import NotFoundPage from './components/pages/NotFound/NotFoundPage';
import ReelsPage from './components/pages/Reels/ReelsPage';

const AppContent = () => {
  const { isDarkMode } = useTheme();

  return (
    <Router>
      <div className={`App min-h-screen transition-colors duration-200 ${
        isDarkMode ? 'dark bg-gray-900' : 'bg-white'
      }`}>
          <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/shop" element={<ShopPage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/signup" element={<SignupPage />} />
          <Route path="/cart" element={<CartPage />} />
          <Route path="/checkout" element={<CheckoutPage />} />
          <Route path="/product/:id" element={<ProductDetailPage />} />
          <Route path="/new-arrivals" element={<NewArrivalsPage />} />
          <Route path="/sale" element={<SalePage />} />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/contact" element={<ContactPage />} />
          <Route path="/reels" element={<ReelsPage />} />
          <Route path="*" element={<NotFoundPage />} />
          {/* Add more routes as needed */}
        </Routes>
      </div>
    </Router>
  );
};

function App() {
  return (
    <ThemeProvider>
      <AppContent />
    </ThemeProvider>
  );
}

export default App;
