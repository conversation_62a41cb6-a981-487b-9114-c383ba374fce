import { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { QueryClient, QueryClientProvider } from 'react-query';

// Stores
import useAuthStore from './stores/authStore';
import useCartStore from './stores/cartStore';

// Components
import Navbar from './components/layout/Navbar';
import Footer from './components/layout/Footer';
import Cart from './components/cart/Cart';
import ProtectedRoute from './components/auth/ProtectedRoute';
import AdminRoute from './components/auth/AdminRoute';

// Pages - Auth
import Login from './pages/auth/Login';
import Signup from './pages/auth/Signup';

// Pages - Main
import Home from './pages/Home';
import Products from './pages/Products';
import Checkout from './pages/Checkout';
import Orders from './pages/Orders';

// Admin Pages
import AdminLayout from './components/admin/AdminLayout';
import AdminDashboard from './pages/admin/Dashboard';
import AdminProducts from './pages/admin/Products';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  const { getCurrentUser, isAuthenticated } = useAuthStore();
  const { loadCart, syncCart } = useCartStore();

  useEffect(() => {
    // Initialize auth state
    if (isAuthenticated) {
      getCurrentUser().then((result) => {
        if (result.success) {
          // Sync cart when user is authenticated
          syncCart(result.user.cart || []);
        }
      });
    }
  }, [getCurrentUser, isAuthenticated, syncCart]);

  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className="min-h-screen bg-white flex flex-col">
          <Navbar />
          <main className="flex-1">
            <Routes>
              {/* Public Routes */}
              <Route path="/" element={<Home />} />
              <Route path="/products" element={<Products />} />
              <Route path="/login" element={<Login />} />
              <Route path="/signup" element={<Signup />} />

              {/* Protected Routes */}
              <Route path="/checkout" element={
                <ProtectedRoute>
                  <Checkout />
                </ProtectedRoute>
              } />
              <Route path="/orders" element={
                <ProtectedRoute>
                  <Orders />
                </ProtectedRoute>
              } />

              {/* Admin Routes */}
              <Route path="/admin" element={
                <AdminRoute>
                  <AdminLayout />
                </AdminRoute>
              }>
                <Route index element={<Navigate to="/admin/dashboard" replace />} />
                <Route path="dashboard" element={<AdminDashboard />} />
                <Route path="products" element={<AdminProducts />} />
              </Route>

              {/* 404 Route */}
              <Route path="*" element={
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
                    <p className="text-gray-600 mb-8">Page not found</p>
                    <a href="/" className="text-pink-600 hover:text-pink-500">
                      Go back home
                    </a>
                  </div>
                </div>
              } />
            </Routes>
          </main>
          <Footer />
          <Cart />
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
            }}
          />
        </div>
      </Router>
    </QueryClientProvider>
  );
}

export default App;
