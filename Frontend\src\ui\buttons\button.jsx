import React, { forwardRef } from 'react';
import { cn } from '../../utils/cn';

const Button = forwardRef(({
  className,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  leftIcon,
  rightIcon,
  children,
  type = 'button',
  ...props
}, ref) => {

  // Size variants
  const sizeClasses = {
    sm: 'h-9 px-3 text-sm font-medium',
    md: 'h-11 px-6 text-base font-medium',
    lg: 'h-13 px-8 text-lg font-semibold'
  };

  // Variant styles
  const variantClasses = {
    primary: `
      bg-blue-600 text-white border border-blue-600
      hover:bg-blue-700 hover:border-blue-700
      active:bg-blue-800 active:border-blue-800
      focus:ring-2 focus:ring-blue-500/50
      disabled:bg-blue-300 disabled:border-blue-300
    `,
    secondary: `
      bg-gray-600 text-white border border-gray-600
      hover:bg-gray-700 hover:border-gray-700
      active:bg-gray-800 active:border-gray-800
      focus:ring-2 focus:ring-gray-500/50
      disabled:bg-gray-300 disabled:border-gray-300
    `,
    outline: `
      bg-transparent text-blue-600 border border-blue-600
      hover:bg-blue-50 hover:text-blue-700
      active:bg-blue-100
      focus:ring-2 focus:ring-blue-500/50
      disabled:text-blue-300 disabled:border-blue-300 disabled:bg-transparent
    `,
    ghost: `
      bg-transparent text-gray-700 border border-transparent
      hover:bg-gray-100 hover:text-gray-900
      active:bg-gray-200
      focus:ring-2 focus:ring-gray-500/50
      disabled:text-gray-400 disabled:bg-transparent
    `,
    destructive: `
      bg-red-600 text-white border border-red-600
      hover:bg-red-700 hover:border-red-700
      active:bg-red-800 active:border-red-800
      focus:ring-2 focus:ring-red-500/50
      disabled:bg-red-300 disabled:border-red-300
    `,
    success: `
      bg-green-600 text-white border border-green-600
      hover:bg-green-700 hover:border-green-700
      active:bg-green-800 active:border-green-800
      focus:ring-2 focus:ring-green-500/50
      disabled:bg-green-300 disabled:border-green-300
    `,
    warning: `
      bg-yellow-500 text-white border border-yellow-500
      hover:bg-yellow-600 hover:border-yellow-600
      active:bg-yellow-700 active:border-yellow-700
      focus:ring-2 focus:ring-yellow-500/50
      disabled:bg-yellow-300 disabled:border-yellow-300
    `
  };

  // Base button classes
  const baseClasses = `
    inline-flex items-center justify-center gap-2 rounded-lg
    font-medium transition-all duration-200 ease-in-out
    focus:outline-none focus:ring-offset-2
    disabled:cursor-not-allowed disabled:opacity-60
    select-none whitespace-nowrap
  `;

  // Loading spinner component
  const LoadingSpinner = () => (
    <svg
      className="animate-spin h-4 w-4"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );

  const buttonClasses = cn(
    baseClasses,
    sizeClasses[size],
    variantClasses[variant],
    className
  );

  return (
    <button
      ref={ref}
      type={type}
      disabled={disabled || loading}
      className={buttonClasses}
      {...props}
    >
      {loading ? (
        <>
          <LoadingSpinner />
          {children && <span>Loading...</span>}
        </>
      ) : (
        <>
          {leftIcon && !loading && (
            <span className="flex-shrink-0">
              {leftIcon}
            </span>
          )}

          {children && (
            <span className="flex-1">
              {children}
            </span>
          )}

          {rightIcon && !loading && (
            <span className="flex-shrink-0">
              {rightIcon}
            </span>
          )}
        </>
      )}
    </button>
  );
});

Button.displayName = 'Button';

export default Button;