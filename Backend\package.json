{"name": "nagma-fashion-backend", "version": "1.0.0", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ecommerce", "fashion", "admin", "api"], "author": "Nagma Fashion", "license": "ISC", "description": "Complete backend for Nagma Fashion e-commerce with admin panel", "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^2.6.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.4.1", "express-validator": "^7.2.0", "googleapis": "^144.0.0", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^6.9.17", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "razorpay": "^2.9.4", "sharp": "^0.33.5", "slugify": "^1.6.6", "uuid": "^11.0.3"}, "devDependencies": {"nodemon": "^3.1.9"}}