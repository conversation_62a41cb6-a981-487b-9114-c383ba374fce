const express = require('express');
const Razorpay = require('razorpay');
const crypto = require('crypto');
const { body, validationResult } = require('express-validator');
const Order = require('../models/Order');
const { auth } = require('../middleware/auth');

const router = express.Router();

// Initialize Razorpay
const razorpay = new Razorpay({
    key_id: process.env.RAZORPAY_KEY_ID,
    key_secret: process.env.RAZORPAY_KEY_SECRET
});

// Create Razorpay order
router.post('/create-order', auth, [
    body('orderId').isMongoId().withMessage('Valid order ID is required'),
    body('amount').isFloat({ min: 1 }).withMessage('Amount must be greater than 0')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { orderId, amount } = req.body;

        // Verify order exists and belongs to user
        const order = await Order.findById(orderId);
        if (!order) {
            return res.status(404).json({
                success: false,
                message: 'Order not found'
            });
        }

        if (order.user.toString() !== req.user.id) {
            return res.status(403).json({
                success: false,
                message: 'Access denied'
            });
        }

        // Verify amount matches order total
        if (Math.round(amount * 100) !== Math.round(order.total * 100)) {
            return res.status(400).json({
                success: false,
                message: 'Amount mismatch'
            });
        }

        // Create Razorpay order
        const razorpayOrder = await razorpay.orders.create({
            amount: Math.round(amount * 100), // Amount in paise
            currency: 'INR',
            receipt: order.orderNumber,
            notes: {
                orderId: order._id.toString(),
                userId: req.user.id
            }
        });

        // Update order with Razorpay order ID
        order.payment.razorpayOrderId = razorpayOrder.id;
        await order.save();

        res.json({
            success: true,
            data: {
                razorpayOrderId: razorpayOrder.id,
                amount: razorpayOrder.amount,
                currency: razorpayOrder.currency,
                key: process.env.RAZORPAY_KEY_ID,
                orderNumber: order.orderNumber,
                customerDetails: {
                    name: req.user.name,
                    email: req.user.email,
                    phone: req.user.phone || order.shippingAddress.phone
                }
            }
        });

    } catch (error) {
        console.error('Create Razorpay order error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create payment order',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Verify payment
router.post('/verify-payment', auth, [
    body('razorpay_order_id').notEmpty().withMessage('Razorpay order ID is required'),
    body('razorpay_payment_id').notEmpty().withMessage('Razorpay payment ID is required'),
    body('razorpay_signature').notEmpty().withMessage('Razorpay signature is required'),
    body('orderId').isMongoId().withMessage('Valid order ID is required')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const {
            razorpay_order_id,
            razorpay_payment_id,
            razorpay_signature,
            orderId
        } = req.body;

        // Verify signature
        const body = razorpay_order_id + '|' + razorpay_payment_id;
        const expectedSignature = crypto
            .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
            .update(body.toString())
            .digest('hex');

        if (expectedSignature !== razorpay_signature) {
            return res.status(400).json({
                success: false,
                message: 'Invalid payment signature'
            });
        }

        // Find and update order
        const order = await Order.findById(orderId);
        if (!order) {
            return res.status(404).json({
                success: false,
                message: 'Order not found'
            });
        }

        if (order.user.toString() !== req.user.id) {
            return res.status(403).json({
                success: false,
                message: 'Access denied'
            });
        }

        // Update payment details
        order.payment.status = 'completed';
        order.payment.razorpayPaymentId = razorpay_payment_id;
        order.payment.razorpaySignature = razorpay_signature;
        order.payment.paidAt = new Date();
        order.status = 'confirmed';

        // Add tracking update
        order.addTracking('confirmed', 'Payment received and order confirmed', null, null);

        await order.save();

        // Get payment details from Razorpay
        try {
            const payment = await razorpay.payments.fetch(razorpay_payment_id);
            
            res.json({
                success: true,
                message: 'Payment verified successfully',
                data: {
                    order,
                    payment: {
                        id: payment.id,
                        amount: payment.amount / 100,
                        currency: payment.currency,
                        method: payment.method,
                        status: payment.status,
                        createdAt: new Date(payment.created_at * 1000)
                    }
                }
            });
        } catch (paymentFetchError) {
            console.error('Error fetching payment details:', paymentFetchError);
            
            res.json({
                success: true,
                message: 'Payment verified successfully',
                data: { order }
            });
        }

    } catch (error) {
        console.error('Verify payment error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to verify payment',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Handle payment failure
router.post('/payment-failed', auth, [
    body('razorpay_order_id').notEmpty().withMessage('Razorpay order ID is required'),
    body('orderId').isMongoId().withMessage('Valid order ID is required'),
    body('error').notEmpty().withMessage('Error details are required')
], async (req, res) => {
    try {
        const { razorpay_order_id, orderId, error } = req.body;

        const order = await Order.findById(orderId);
        if (!order) {
            return res.status(404).json({
                success: false,
                message: 'Order not found'
            });
        }

        if (order.user.toString() !== req.user.id) {
            return res.status(403).json({
                success: false,
                message: 'Access denied'
            });
        }

        // Update payment status
        order.payment.status = 'failed';
        order.addTracking('pending', `Payment failed: ${error.description || 'Unknown error'}`, null, null);

        await order.save();

        res.json({
            success: true,
            message: 'Payment failure recorded',
            data: { order }
        });

    } catch (error) {
        console.error('Payment failure error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to record payment failure',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Webhook for Razorpay events
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
    try {
        const signature = req.headers['x-razorpay-signature'];
        const body = req.body;

        // Verify webhook signature
        const expectedSignature = crypto
            .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET || process.env.RAZORPAY_KEY_SECRET)
            .update(body)
            .digest('hex');

        if (signature !== expectedSignature) {
            return res.status(400).json({ error: 'Invalid signature' });
        }

        const event = JSON.parse(body);

        switch (event.event) {
            case 'payment.captured':
                await handlePaymentCaptured(event.payload.payment.entity);
                break;
            case 'payment.failed':
                await handlePaymentFailed(event.payload.payment.entity);
                break;
            case 'order.paid':
                await handleOrderPaid(event.payload.order.entity);
                break;
            default:
                console.log('Unhandled webhook event:', event.event);
        }

        res.json({ status: 'ok' });

    } catch (error) {
        console.error('Webhook error:', error);
        res.status(500).json({ error: 'Webhook processing failed' });
    }
});

// Helper functions for webhook events
async function handlePaymentCaptured(payment) {
    try {
        const order = await Order.findOne({
            'payment.razorpayOrderId': payment.order_id
        });

        if (order && order.payment.status !== 'completed') {
            order.payment.status = 'completed';
            order.payment.razorpayPaymentId = payment.id;
            order.payment.paidAt = new Date(payment.created_at * 1000);
            order.status = 'confirmed';
            
            order.addTracking('confirmed', 'Payment captured successfully', null, null);
            await order.save();
        }
    } catch (error) {
        console.error('Error handling payment captured:', error);
    }
}

async function handlePaymentFailed(payment) {
    try {
        const order = await Order.findOne({
            'payment.razorpayOrderId': payment.order_id
        });

        if (order) {
            order.payment.status = 'failed';
            order.addTracking('pending', `Payment failed: ${payment.error_description || 'Unknown error'}`, null, null);
            await order.save();
        }
    } catch (error) {
        console.error('Error handling payment failed:', error);
    }
}

async function handleOrderPaid(razorpayOrder) {
    try {
        const order = await Order.findOne({
            'payment.razorpayOrderId': razorpayOrder.id
        });

        if (order && order.payment.status !== 'completed') {
            order.payment.status = 'completed';
            order.payment.paidAt = new Date();
            order.status = 'confirmed';
            
            order.addTracking('confirmed', 'Order paid successfully', null, null);
            await order.save();
        }
    } catch (error) {
        console.error('Error handling order paid:', error);
    }
}

// Get payment status
router.get('/status/:orderId', auth, async (req, res) => {
    try {
        const { orderId } = req.params;

        const order = await Order.findById(orderId);
        if (!order) {
            return res.status(404).json({
                success: false,
                message: 'Order not found'
            });
        }

        if (order.user.toString() !== req.user.id) {
            return res.status(403).json({
                success: false,
                message: 'Access denied'
            });
        }

        res.json({
            success: true,
            data: {
                paymentStatus: order.payment.status,
                orderStatus: order.status,
                paymentMethod: order.payment.method,
                paidAt: order.payment.paidAt,
                amount: order.total
            }
        });

    } catch (error) {
        console.error('Get payment status error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get payment status',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

module.exports = router;
