import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../ui';

const Hero = () => {
  const navigate = useNavigate();

  return (
    <div className="relative bg-gradient-to-r from-pink-50 to-purple-50 overflow-hidden">
      <div className="max-w-7xl mx-auto">
        <div className="relative z-10 pb-8 sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
          <main className="mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
            <div className="sm:text-center lg:text-left">
              <h1 className="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
                <span className="block xl:inline">Discover Your</span>{' '}
                <span className="block text-pink-600 xl:inline">Perfect Style</span>
              </h1>
              <p className="mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0">
                Curated by Nagma - Explore our exclusive collection of trendy women's fashion. 
                From casual wear to elegant dresses, find pieces that express your unique personality.
              </p>
              <div className="mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start">
                <div className="rounded-md shadow">
                  <Button 
                    size="lg"
                    onClick={() => navigate('/shop')}
                    className="w-full flex items-center justify-center px-8 py-3 text-base font-medium"
                  >
                    Shop Now
                  </Button>
                </div>
                <div className="mt-3 sm:mt-0 sm:ml-3">
                  <Button 
                    variant="outline" 
                    size="lg"
                    onClick={() => navigate('/shop?category=new')}
                    className="w-full flex items-center justify-center px-8 py-3 text-base font-medium"
                  >
                    New Arrivals
                  </Button>
                </div>
              </div>
              
              {/* Stats */}
              <div className="mt-8 grid grid-cols-3 gap-4 sm:gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-pink-600">1000+</div>
                  <div className="text-sm text-gray-500">Happy Customers</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-pink-600">500+</div>
                  <div className="text-sm text-gray-500">Products</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-pink-600">50+</div>
                  <div className="text-sm text-gray-500">Brands</div>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
      
      {/* Hero Image */}
      <div className="lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2">
        <div className="h-56 w-full bg-gradient-to-br from-pink-200 to-purple-300 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center">
          {/* Placeholder for hero image */}
          <div className="text-center text-white">
            <div className="text-6xl mb-4">👗</div>
            <div className="text-xl font-semibold">Fashion Collection</div>
            <div className="text-sm opacity-90">Curated by Nagma</div>
          </div>
        </div>
      </div>
      
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 bg-pink-200 rounded-full opacity-20"></div>
      <div className="absolute bottom-0 left-0 -mb-8 -ml-8 w-32 h-32 bg-purple-200 rounded-full opacity-20"></div>
    </div>
  );
};

export default Hero;
