import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../ui';
import { useTheme } from '../../context/ThemeContext';

const Hero = () => {
  const navigate = useNavigate();
  const [currentSlide, setCurrentSlide] = useState(0);
  const { isDarkMode } = useTheme();

  // Carousel images data
  const carouselImages = [
    {
      id: 1,
      title: "New Collection",
      subtitle: "Fashion Forward",
      image: "/images/frame1.png",
      alt: "Nagma Fashion Frame 1 Collection",
      description: "Discover our latest curated pieces"
    },
    {
      id: 2,
      title: "Instagram Favorites",
      subtitle: "Trending Now",
      image: "/images/instagram-post.png",
      alt: "Nagma Fashion Instagram Post",
      description: "Most loved styles from our community"
    },
    {
      id: 3,
      title: "Exclusive Styles",
      subtitle: "Limited Edition",
      image: "/images/frame4.png",
      alt: "Nagma Fashion Frame 4 Collection",
      description: "Exclusive pieces you won't find anywhere else"
    }
  ];

  // Auto-play carousel
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % carouselImages.length);
    }, 4000); // Change slide every 4 seconds

    return () => clearInterval(interval);
  }, [carouselImages.length]);

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % carouselImages.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + carouselImages.length) % carouselImages.length);
  };

  return (
    <div className={`relative ${isDarkMode ? 'bg-gradient-to-r from-gray-900 to-gray-800' : 'bg-gradient-to-r from-pink-50 to-purple-50'} overflow-hidden`}>
      <div className="max-w-7xl mx-auto">
        <div className="relative z-10 pb-8 sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
          <main className="mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
            <div className="sm:text-center lg:text-left">
              <h1 className={`text-4xl tracking-tight font-extrabold ${isDarkMode ? 'text-white' : 'text-gray-900'} sm:text-5xl md:text-6xl`}>
                <span className="block xl:inline">Discover Your</span>{' '}
                <span className="block text-pink-600 xl:inline">Perfect Style</span>
              </h1>
              <p className={`mt-3 text-base ${isDarkMode ? 'text-gray-300' : 'text-gray-500'} sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0`}>
                Curated by Nagma - Explore our exclusive collection of trendy women's fashion.
                From casual wear to elegant dresses, find pieces that express your unique personality.
              </p>
              <div className="mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start">
                <div className="rounded-md shadow">
                  <Button
                    size="lg"
                    onClick={() => navigate('/shop')}
                    className="w-full flex items-center justify-center px-8 py-3 text-base font-medium"
                  >
                    Shop Now
                  </Button>
                </div>
                <div className="mt-3 sm:mt-0 sm:ml-3">
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={() => navigate('/new-arrivals')}
                    className="w-full flex items-center justify-center px-8 py-3 text-base font-medium"
                  >
                    New Arrivals
                  </Button>
                </div>
              </div>

              {/* Stats */}
              <div className="mt-8 grid grid-cols-3 gap-4 sm:gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-pink-600">1000+</div>
                  <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Happy Customers</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-pink-600">500+</div>
                  <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Products</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-pink-600">50+</div>
                  <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Brands</div>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>

      {/* Hero Carousel */}
      <div className="lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2">
        <div className="relative h-56 w-full sm:h-72 md:h-96 lg:w-full lg:h-full">
          {/* Carousel Container */}
          <div className="relative h-full rounded-bl-[80px] overflow-hidden shadow-2xl">
            {carouselImages.map((slide, index) => (
              <div
                key={slide.id}
                className={`absolute inset-0 transition-all duration-1000 ease-in-out transform ${
                  index === currentSlide
                    ? 'translate-x-0 opacity-100 scale-100'
                    : index < currentSlide
                    ? 'translate-x-full opacity-0 scale-95 rotate-12'
                    : '-translate-x-full opacity-0 scale-95 -rotate-12'
                }`}
              >
                <div className="h-full w-full relative group">
                  {/* Background Image */}
                  <img
                    src={slide.image}
                    alt={slide.alt}
                    className="w-full h-full object-cover transform scale-105 group-hover:scale-110 transition-transform duration-700"
                    loading="lazy"
                    onError={(e) => {
                      console.error('Failed to load image:', slide.image);
                      e.target.style.display = 'none';
                    }}
                  />
                  
                  {/* Gradient Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-tr from-black/60 via-black/30 to-transparent">
                    {/* Animated Pattern Overlay */}
                    <div className="absolute inset-0 opacity-30 mix-blend-overlay bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.2)_1px,transparent_1px)] bg-[length:20px_20px]"></div>
                  </div>

                  {/* Content Container */}
                  <div className="absolute inset-0 flex flex-col justify-end p-8 lg:p-12">
                    {/* Text Content with Animation */}
                    <div className="transform translate-y-8 group-hover:translate-y-0 opacity-0 group-hover:opacity-100 transition-all duration-500">
                      <h3 className="text-3xl lg:text-4xl font-bold text-white mb-2 tracking-tight">
                        {slide.title}
                      </h3>
                      <p className="text-lg text-white/90 mb-3">
                        {slide.subtitle}
                      </p>
                      <p className="text-sm text-white/80">
                        {slide.description}
                      </p>
                      
                      {/* Shop Now Button */}
                      <button className="mt-6 px-6 py-2 bg-white text-black rounded-full font-medium 
                        transform hover:scale-105 transition-all duration-300 
                        hover:bg-pink-50 hover:shadow-lg hover:shadow-pink-500/20">
                        Shop Collection
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* Navigation Controls */}
            <div className="absolute bottom-8 right-8 flex items-center space-x-4 z-20">
              <button
                onClick={prevSlide}
                className="p-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 
                  hover:bg-white/20 transition-all duration-300 group"
              >
                <svg className="w-6 h-6 text-white transform group-hover:scale-110 transition-transform" 
                  fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>

              <div className="flex space-x-2">
                {carouselImages.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToSlide(index)}
                    className={`w-2 h-2 rounded-full transition-all duration-300 
                      ${index === currentSlide 
                        ? 'w-8 bg-white' 
                        : 'bg-white/50 hover:bg-white/75'}`}
                    aria-label={`Go to slide ${index + 1}`}
                  />
                ))}
              </div>

              <button
                onClick={nextSlide}
                className="p-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 
                  hover:bg-white/20 transition-all duration-300 group"
              >
                <svg className="w-6 h-6 text-white transform group-hover:scale-110 transition-transform" 
                  fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>

            {/* Progress Bar */}
            <div className="absolute bottom-0 left-0 w-full h-1 bg-white/10">
              <div
                className="h-full bg-gradient-to-r from-pink-500 to-purple-500 transition-all duration-1000 ease-out"
                style={{
                  width: `${((currentSlide + 1) / carouselImages.length) * 100}%`
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className={`absolute top-0 right-0 w-96 h-96 ${isDarkMode ? 'bg-gradient-to-br from-pink-500/10 to-purple-500/10' : 'bg-gradient-to-br from-pink-500/20 to-purple-500/20'} rounded-full filter blur-3xl opacity-30 -z-10 transform translate-x-1/2 -translate-y-1/2`}></div>
      <div className={`absolute bottom-0 left-0 w-96 h-96 ${isDarkMode ? 'bg-gradient-to-tr from-purple-500/10 to-pink-500/10' : 'bg-gradient-to-tr from-purple-500/20 to-pink-500/20'} rounded-full filter blur-3xl opacity-30 -z-10 transform -translate-x-1/2 translate-y-1/2`}></div>
    </div>
  );
};

export default Hero;
