# Nagma Fashion Frontend

A modern, responsive e-commerce frontend built with React, Vite, and Tailwind CSS.

## 🚀 Features

### User Features
- **Authentication System** - Login, Signup, Google OAuth
- **Product Browsing** - Search, filter, and sort products
- **Shopping Cart** - Add/remove items, quantity management
- **Checkout Process** - Razorpay payment integration
- **Order Management** - Order history, tracking, cancellation
- **User Profile** - Profile management, addresses, preferences
- **Wishlist** - Save favorite products
- **Responsive Design** - Mobile-first approach

### Admin Features
- **Admin Dashboard** - Analytics, charts, overview
- **Product Management** - CRUD operations, image upload
- **Category Management** - Hierarchical categories
- **Order Management** - Status updates, tracking
- **User Management** - User roles, status management
- **Reels Management** - Video content management
- **Email Campaigns** - Newsletter, promotions

## 🛠️ Tech Stack

- **React 19** - UI library
- **Vite** - Build tool
- **Tailwind CSS** - Styling
- **React Router** - Navigation
- **Zustand** - State management
- **React Query** - Data fetching
- **React Hook Form** - Form handling
- **Framer Motion** - Animations
- **Headless UI** - Accessible components
- **Heroicons** - Icons
- **Chart.js** - Charts for admin
- **React Hot Toast** - Notifications

## 📦 Installation

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### 1. Install Dependencies
```bash
npm install

# Or install specific packages if needed:
npm install @headlessui/react @heroicons/react axios chart.js date-fns framer-motion react-chartjs-2 react-dropzone react-hook-form react-hot-toast react-query react-table recharts zustand
```

### 2. Environment Setup
Create a `.env` file in the root directory:

```env
VITE_API_URL=http://localhost:5000/api
VITE_FRONTEND_URL=http://localhost:5174
VITE_RAZORPAY_KEY_ID=your-razorpay-key-id
VITE_GOOGLE_CLIENT_ID=your-google-client-id
VITE_CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
VITE_CLOUDINARY_UPLOAD_PRESET=your-upload-preset
```

### 3. Start Development Server
```bash
npm run dev
```

The application will be available at `http://localhost:5174`

## 📁 Project Structure

```
Frontend/
├── public/                 # Static assets
├── src/
│   ├── components/         # Reusable components
│   │   ├── admin/         # Admin-specific components
│   │   ├── auth/          # Authentication components
│   │   ├── cart/          # Shopping cart components
│   │   └── layout/        # Layout components
│   ├── pages/             # Page components
│   │   ├── admin/         # Admin pages
│   │   └── auth/          # Auth pages
│   ├── services/          # API services
│   ├── stores/            # Zustand stores
│   ├── ui/                # UI components
│   ├── utils/             # Utility functions
│   ├── App.jsx            # Main app component
│   └── main.jsx           # Entry point
├── .env                   # Environment variables
├── package.json           # Dependencies
└── README.md             # This file
```

## 🔐 Authentication

The app supports multiple authentication methods:

1. **Email/Password** - Traditional signup/login
2. **Google OAuth** - Social login
3. **JWT Tokens** - Secure session management
4. **Role-based Access** - User/Admin/SuperAdmin roles

## 🛒 E-commerce Features

### Shopping Cart
- Add/remove products
- Quantity management
- Local storage for guests
- Server sync for authenticated users
- Real-time price calculations

### Checkout Process
1. **Cart Review** - Verify items and quantities
2. **Shipping Details** - Address management
3. **Payment Method** - Razorpay or Cash on Delivery
4. **Order Confirmation** - Email notifications

### Payment Integration
- **Razorpay Gateway** - Cards, UPI, Net Banking
- **Cash on Delivery** - Pay on delivery option
- **Secure Processing** - PCI compliant
- **Webhook Handling** - Real-time status updates

## 👨‍💼 Admin Panel

### Dashboard Features
- **Revenue Analytics** - Charts and graphs
- **User Metrics** - Growth and engagement
- **Order Statistics** - Status breakdown
- **Product Performance** - Best sellers
- **Low Stock Alerts** - Inventory management

### Management Tools
- **Product CRUD** - Full product lifecycle
- **Category Management** - Hierarchical organization
- **Order Processing** - Status updates and tracking
- **User Administration** - Role and status management
- **Content Management** - Reels and media

## 📱 Responsive Design

The application is fully responsive with:
- **Mobile-first** approach
- **Breakpoint system** (sm, md, lg, xl)
- **Touch-friendly** interfaces
- **Optimized images** and loading
- **Progressive enhancement**

## 🎨 UI/UX Features

### Design System
- **Consistent spacing** and typography
- **Color palette** with brand colors
- **Component library** for reusability
- **Accessibility** features
- **Dark mode** support (planned)

### Animations
- **Page transitions** with Framer Motion
- **Micro-interactions** for better UX
- **Loading states** and skeletons
- **Hover effects** and feedback

## 🔧 Development

### Available Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
```

### Code Style
- **ESLint** configuration
- **Prettier** formatting
- **Component naming** conventions
- **File organization** standards

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Environment Variables for Production
```env
VITE_API_URL=https://your-api-domain.com/api
VITE_FRONTEND_URL=https://your-domain.com
VITE_RAZORPAY_KEY_ID=your-production-razorpay-key
VITE_GOOGLE_CLIENT_ID=your-production-google-client-id
```

### Deployment Options
- **Vercel** - Recommended for React apps
- **Netlify** - Easy deployment with git integration
- **AWS S3 + CloudFront** - Scalable hosting
- **Firebase Hosting** - Google's hosting solution

## 🧪 Testing

### Testing Strategy
- **Unit Tests** - Component testing
- **Integration Tests** - API integration
- **E2E Tests** - User journey testing
- **Visual Regression** - UI consistency

### Testing Tools (Planned)
- **Vitest** - Unit testing
- **React Testing Library** - Component testing
- **Cypress** - E2E testing
- **Storybook** - Component documentation

## 📈 Performance

### Optimization Features
- **Code Splitting** - Route-based chunks
- **Lazy Loading** - Components and images
- **Bundle Analysis** - Size optimization
- **Caching Strategy** - API and assets
- **Image Optimization** - WebP format

### Performance Metrics
- **Lighthouse Score** - 90+ target
- **Core Web Vitals** - Google standards
- **Bundle Size** - < 500KB gzipped
- **Load Time** - < 3 seconds

## 🔒 Security

### Security Measures
- **XSS Protection** - Input sanitization
- **CSRF Protection** - Token validation
- **Secure Headers** - Content Security Policy
- **Authentication** - JWT token security
- **Data Validation** - Client and server-side

## 📞 Support

For support and questions:
- **Documentation** - Check this README
- **Issues** - Create GitHub issues
- **Email** - Contact development team

## 📄 License

This project is proprietary and confidential.

---

**Nagma Fashion Frontend** - Built with ❤️ for the fashion industry
