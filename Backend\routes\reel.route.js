const express = require('express');
const { body, query, validationResult } = require('express-validator');
const Reel = require('../models/Reel');
const { auth, adminAuth, optionalAuth } = require('../middleware/auth');
const { uploadReel } = require('../config/cloudinary');

const router = express.Router();

// Get all reels with pagination and filtering
router.get('/', optionalAuth, [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
    query('category').optional().isIn(['fashion', 'styling', 'tutorial', 'behind_scenes', 'product_showcase', 'trend_alert']).withMessage('Invalid category'),
    query('featured').optional().isBoolean().withMessage('Featured must be a boolean'),
    query('search').optional().isLength({ min: 1 }).withMessage('Search query cannot be empty')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const {
            page = 1,
            limit = 12,
            category,
            featured,
            search,
            sort = 'newest'
        } = req.query;

        // Build filter
        const filter = { isActive: true };
        if (category) filter.category = category;
        if (featured !== undefined) filter.isFeatured = featured === 'true';
        if (search) {
            filter.$or = [
                { title: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } },
                { tags: { $in: [new RegExp(search, 'i')] } },
                { hashtags: { $in: [new RegExp(search, 'i')] } }
            ];
        }

        // Build sort
        let sortObj = {};
        switch (sort) {
            case 'newest':
                sortObj = { createdAt: -1 };
                break;
            case 'oldest':
                sortObj = { createdAt: 1 };
                break;
            case 'popular':
                sortObj = { views: -1 };
                break;
            case 'liked':
                sortObj = { 'likes.length': -1 };
                break;
            default:
                sortObj = { createdAt: -1 };
        }

        const skip = (parseInt(page) - 1) * parseInt(limit);

        const [reels, total] = await Promise.all([
            Reel.find(filter)
                .populate('createdBy', 'name avatar')
                .populate('featuredProducts.product', 'name images basePrice salePrice')
                .sort(sortObj)
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            Reel.countDocuments(filter)
        ]);

        // Add user interaction data if authenticated
        if (req.user) {
            reels.forEach(reel => {
                reel.isLiked = reel.likes.includes(req.user.id);
                reel.isSaved = reel.saves.includes(req.user.id);
            });
        }

        const totalPages = Math.ceil(total / parseInt(limit));

        res.json({
            success: true,
            data: {
                reels,
                pagination: {
                    currentPage: parseInt(page),
                    totalPages,
                    totalReels: total,
                    hasNextPage: parseInt(page) < totalPages,
                    hasPrevPage: parseInt(page) > 1,
                    limit: parseInt(limit)
                }
            }
        });

    } catch (error) {
        console.error('Get reels error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch reels',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Get single reel
router.get('/:id', optionalAuth, async (req, res) => {
    try {
        const { id } = req.params;

        const reel = await Reel.findById(id)
            .populate('createdBy', 'name avatar')
            .populate('featuredProducts.product', 'name images basePrice salePrice slug')
            .populate('comments.user', 'name avatar')
            .populate('comments.replies.user', 'name avatar')
            .lean();

        if (!reel || !reel.isActive) {
            return res.status(404).json({
                success: false,
                message: 'Reel not found'
            });
        }

        // Add user interaction data if authenticated
        if (req.user) {
            reel.isLiked = reel.likes.includes(req.user.id);
            reel.isSaved = reel.saves.includes(req.user.id);
        }

        // Increment view count (don't await to avoid slowing response)
        Reel.findByIdAndUpdate(id, { $inc: { views: 1 } }).exec();

        res.json({
            success: true,
            data: { reel }
        });

    } catch (error) {
        console.error('Get reel error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch reel',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Create new reel (Admin only)
router.post('/', adminAuth, uploadReel.single('video'), [
    body('title').trim().isLength({ min: 2 }).withMessage('Title must be at least 2 characters'),
    body('description').optional().trim(),
    body('category').isIn(['fashion', 'styling', 'tutorial', 'behind_scenes', 'product_showcase', 'trend_alert']).withMessage('Invalid category'),
    body('tags').optional().isArray().withMessage('Tags must be an array'),
    body('hashtags').optional().isArray().withMessage('Hashtags must be an array'),
    body('featuredProducts').optional().isArray().withMessage('Featured products must be an array')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: 'Video file is required'
            });
        }

        const {
            title,
            description,
            category,
            tags,
            hashtags,
            featuredProducts,
            isFeatured,
            isPinned,
            scheduledAt
        } = req.body;

        // Create reel
        const reel = new Reel({
            title,
            description,
            video: {
                url: req.file.path,
                publicId: req.file.filename,
                duration: req.file.duration || 0
            },
            category,
            tags: tags ? JSON.parse(tags) : [],
            hashtags: hashtags ? JSON.parse(hashtags) : [],
            featuredProducts: featuredProducts ? JSON.parse(featuredProducts) : [],
            isFeatured: isFeatured || false,
            isPinned: isPinned || false,
            scheduledAt: scheduledAt ? new Date(scheduledAt) : null,
            publishedAt: scheduledAt ? null : new Date(),
            createdBy: req.user.id
        });

        await reel.save();

        // Populate for response
        await reel.populate('createdBy', 'name avatar');
        await reel.populate('featuredProducts.product', 'name images basePrice salePrice');

        res.status(201).json({
            success: true,
            message: 'Reel created successfully',
            data: { reel }
        });

    } catch (error) {
        console.error('Create reel error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create reel',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Like/Unlike reel
router.post('/:id/like', auth, async (req, res) => {
    try {
        const { id } = req.params;

        const reel = await Reel.findById(id);
        if (!reel || !reel.isActive) {
            return res.status(404).json({
                success: false,
                message: 'Reel not found'
            });
        }

        const isLiked = reel.toggleLike(req.user.id);
        await reel.save();

        res.json({
            success: true,
            message: isLiked ? 'Reel liked' : 'Reel unliked',
            data: {
                isLiked,
                likeCount: reel.likes.length
            }
        });

    } catch (error) {
        console.error('Toggle like error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to toggle like',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Save/Unsave reel
router.post('/:id/save', auth, async (req, res) => {
    try {
        const { id } = req.params;

        const reel = await Reel.findById(id);
        if (!reel || !reel.isActive) {
            return res.status(404).json({
                success: false,
                message: 'Reel not found'
            });
        }

        const isSaved = reel.toggleSave(req.user.id);
        await reel.save();

        res.json({
            success: true,
            message: isSaved ? 'Reel saved' : 'Reel unsaved',
            data: {
                isSaved,
                saveCount: reel.saves.length
            }
        });

    } catch (error) {
        console.error('Toggle save error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to toggle save',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Add comment to reel
router.post('/:id/comments', auth, [
    body('text').trim().isLength({ min: 1 }).withMessage('Comment text is required')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { id } = req.params;
        const { text } = req.body;

        const reel = await Reel.findById(id);
        if (!reel || !reel.isActive) {
            return res.status(404).json({
                success: false,
                message: 'Reel not found'
            });
        }

        await reel.addComment(req.user.id, text);
        await reel.populate('comments.user', 'name avatar');

        const newComment = reel.comments[reel.comments.length - 1];

        res.status(201).json({
            success: true,
            message: 'Comment added successfully',
            data: {
                comment: newComment,
                commentCount: reel.comments.length
            }
        });

    } catch (error) {
        console.error('Add comment error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to add comment',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Increment share count
router.post('/:id/share', optionalAuth, async (req, res) => {
    try {
        const { id } = req.params;

        const reel = await Reel.findById(id);
        if (!reel || !reel.isActive) {
            return res.status(404).json({
                success: false,
                message: 'Reel not found'
            });
        }

        await reel.incrementShares();

        res.json({
            success: true,
            message: 'Share count updated',
            data: {
                shareCount: reel.shares
            }
        });

    } catch (error) {
        console.error('Increment share error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update share count',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

module.exports = router;
