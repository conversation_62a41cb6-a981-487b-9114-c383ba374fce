import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../ui';

const ProductGrid = ({ filters }) => {
  const navigate = useNavigate();
  const [favorites, setFavorites] = useState(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const productsPerPage = 12;

  // Extended mock product data
  const allProducts = [
    {
      id: 1,
      name: 'Elegant Summer Dress',
      price: 89.99,
      originalPrice: 129.99,
      image: '👗',
      category: 'dresses',
      rating: 4.8,
      reviews: 124,
      isNew: true,
      colors: ['Pink', 'Blue', 'White'],
      sizes: ['S', 'M', 'L', 'XL'],
      brand: 'Nagma Signature'
    },
    {
      id: 2,
      name: 'Casual Chic Blouse',
      price: 45.99,
      originalPrice: null,
      image: '👚',
      category: 'tops',
      rating: 4.6,
      reviews: 89,
      isNew: false,
      colors: ['Black', 'White', 'Navy'],
      sizes: ['XS', 'S', 'M', 'L'],
      brand: 'Urban Chic'
    },
    {
      id: 3,
      name: 'High-Waist Jeans',
      price: 79.99,
      originalPrice: 99.99,
      image: '👖',
      category: 'bottoms',
      rating: 4.9,
      reviews: 156,
      isNew: false,
      colors: ['Blue', 'Black'],
      sizes: ['S', 'M', 'L', 'XL'],
      brand: 'Casual Comfort'
    },
    {
      id: 4,
      name: 'Designer Handbag',
      price: 159.99,
      originalPrice: null,
      image: '👜',
      category: 'accessories',
      rating: 4.7,
      reviews: 67,
      isNew: true,
      colors: ['Brown', 'Black', 'Beige'],
      sizes: ['One Size'],
      brand: 'Designer Collection'
    },
    {
      id: 5,
      name: 'Stylish Heels',
      price: 119.99,
      originalPrice: 149.99,
      image: '👠',
      category: 'shoes',
      rating: 4.5,
      reviews: 93,
      isNew: false,
      colors: ['Black', 'Red', 'Nude'],
      sizes: ['6', '7', '8', '9', '10'],
      brand: 'Elegant Essentials'
    },
    {
      id: 6,
      name: 'Cozy Cardigan',
      price: 69.99,
      originalPrice: null,
      image: '🧥',
      category: 'outerwear',
      rating: 4.8,
      reviews: 112,
      isNew: true,
      colors: ['Cream', 'Gray', 'Pink'],
      sizes: ['S', 'M', 'L'],
      brand: 'Nagma Signature'
    },
    // Add more products...
    {
      id: 7,
      name: 'Floral Midi Dress',
      price: 95.99,
      originalPrice: null,
      image: '🌸',
      category: 'dresses',
      rating: 4.7,
      reviews: 78,
      isNew: true,
      colors: ['Pink', 'Yellow', 'Blue'],
      sizes: ['XS', 'S', 'M', 'L'],
      brand: 'Nagma Signature'
    },
    {
      id: 8,
      name: 'Silk Scarf',
      price: 29.99,
      originalPrice: 39.99,
      image: '🧣',
      category: 'accessories',
      rating: 4.6,
      reviews: 45,
      isNew: false,
      colors: ['Red', 'Blue', 'Green'],
      sizes: ['One Size'],
      brand: 'Elegant Essentials'
    }
  ];

  // Filter and sort products
  const filteredProducts = useMemo(() => {
    let filtered = allProducts.filter(product => {
      // Category filter
      if (filters.category && product.category !== filters.category) {
        return false;
      }

      // Price filter
      if (product.price > filters.priceRange[1]) {
        return false;
      }

      // Size filter
      if (filters.size && !product.sizes.includes(filters.size)) {
        return false;
      }

      // Color filter
      if (filters.color && !product.colors.includes(filters.color)) {
        return false;
      }

      // Brand filter
      if (filters.brand && product.brand !== filters.brand) {
        return false;
      }

      return true;
    });

    // Sort products
    switch (filters.sortBy) {
      case 'newest':
        filtered = filtered.sort((a, b) => b.isNew - a.isNew);
        break;
      case 'price-low':
        filtered = filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        filtered = filtered.sort((a, b) => b.price - a.price);
        break;
      case 'rating':
        filtered = filtered.sort((a, b) => b.rating - a.rating);
        break;
      default:
        // Featured - keep original order
        break;
    }

    return filtered;
  }, [filters]);

  // Pagination
  const totalPages = Math.ceil(filteredProducts.length / productsPerPage);
  const startIndex = (currentPage - 1) * productsPerPage;
  const paginatedProducts = filteredProducts.slice(startIndex, startIndex + productsPerPage);

  const toggleFavorite = (productId) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(productId)) {
        newFavorites.delete(productId);
      } else {
        newFavorites.add(productId);
      }
      return newFavorites;
    });
  };

  const HeartIcon = ({ filled }) => (
    <svg className="w-5 h-5" fill={filled ? "currentColor" : "none"} stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
    </svg>
  );

  const StarIcon = () => (
    <svg className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
    </svg>
  );

  return (
    <div>
      {/* Results Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">
            {filteredProducts.length} Products Found
          </h2>
          <p className="text-sm text-gray-600">
            Showing {startIndex + 1}-{Math.min(startIndex + productsPerPage, filteredProducts.length)} of {filteredProducts.length}
          </p>
        </div>
      </div>

      {/* Products Grid */}
      {paginatedProducts.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {paginatedProducts.map((product) => (
            <div
              key={product.id}
              className="group bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 overflow-hidden"
            >
              {/* Product Image */}
              <div className="relative bg-gradient-to-br from-gray-100 to-gray-200 h-48 flex items-center justify-center cursor-pointer"
                   onClick={() => navigate(`/product/${product.id}`)}>
                <div className="text-4xl group-hover:scale-110 transition-transform duration-300">
                  {product.image}
                </div>
                
                {/* Badges */}
                <div className="absolute top-3 left-3 flex flex-col gap-1">
                  {product.isNew && (
                    <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                      NEW
                    </span>
                  )}
                  {product.originalPrice && (
                    <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                      SALE
                    </span>
                  )}
                </div>

                {/* Favorite Button */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleFavorite(product.id);
                  }}
                  className={`absolute top-3 right-3 p-2 rounded-full transition-all duration-300 ${
                    favorites.has(product.id)
                      ? 'bg-red-500 text-white'
                      : 'bg-white text-gray-600 hover:bg-red-50 hover:text-red-500'
                  }`}
                >
                  <HeartIcon filled={favorites.has(product.id)} />
                </button>
              </div>

              {/* Product Info */}
              <div className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-xs text-gray-500 font-medium uppercase tracking-wide">
                    {product.brand}
                  </span>
                  <div className="flex items-center space-x-1">
                    <StarIcon />
                    <span className="text-sm text-gray-600">{product.rating}</span>
                  </div>
                </div>

                <h3 className="text-sm font-semibold text-gray-900 mb-2 line-clamp-2 cursor-pointer hover:text-pink-600 transition-colors duration-300"
                    onClick={() => navigate(`/product/${product.id}`)}>
                  {product.name}
                </h3>

                {/* Price */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">${product.price}</span>
                    {product.originalPrice && (
                      <span className="text-sm text-gray-500 line-through">${product.originalPrice}</span>
                    )}
                  </div>
                  {product.originalPrice && (
                    <span className="text-xs font-semibold text-green-600">
                      {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF
                    </span>
                  )}
                </div>

                {/* Quick Actions */}
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 text-xs"
                    onClick={() => navigate(`/product/${product.id}`)}
                  >
                    View
                  </Button>
                  <Button
                    variant="primary"
                    size="sm"
                    className="flex-1 text-xs"
                    onClick={() => {
                      // Add to cart logic
                      navigate('/cart');
                    }}
                  >
                    Add to Cart
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔍</div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No products found</h3>
          <p className="text-gray-600 mb-4">Try adjusting your filters to see more results</p>
          <Button variant="outline" onClick={() => window.location.reload()}>
            Clear All Filters
          </Button>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(currentPage - 1)}
            >
              Previous
            </Button>
            
            {[...Array(totalPages)].map((_, index) => (
              <Button
                key={index + 1}
                variant={currentPage === index + 1 ? "primary" : "outline"}
                size="sm"
                onClick={() => setCurrentPage(index + 1)}
              >
                {index + 1}
              </Button>
            ))}
            
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(currentPage + 1)}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductGrid;
