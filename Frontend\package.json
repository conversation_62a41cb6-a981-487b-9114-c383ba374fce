{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "axios": "^1.7.9", "chart.js": "^4.4.7", "date-fns": "^4.1.0", "framer-motion": "^11.15.0", "react": "^19.1.0", "react-chartjs-2": "^5.2.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "react-router-dom": "^7.6.1", "react-table": "^7.8.0", "recharts": "^2.15.0", "tailwindcss": "^4.1.8", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}