import React from 'react';
import { Link } from 'react-router-dom';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const footerSections = [
    {
      title: 'Shop',
      links: [
        { name: 'New Arrivals', path: '/new-arrivals' },
        { name: 'Dress<PERSON>', path: '/shop?category=dresses' },
        { name: 'Tops', path: '/shop?category=tops' },
        { name: 'Bottoms', path: '/shop?category=bottoms' },
        { name: 'Accessories', path: '/shop?category=accessories' },
        { name: 'Sale', path: '/sale' }
      ]
    },
    {
      title: 'Customer Care',
      links: [
        { name: 'Contact Us', path: '/contact' },
        { name: 'Size Guide', path: '/size-guide' },
        { name: 'Shipping Info', path: '/shipping' },
        { name: 'Returns', path: '/returns' },
        { name: 'FAQ', path: '/contact' },
        { name: 'Track Order', path: '/track-order' }
      ]
    },
    {
      title: 'About',
      links: [
        { name: 'Our Story', path: '/about' },
        { name: 'Nagma\'s Reels', path: '/reels' },
        { name: 'Sustainability', path: '/sustainability' },
        { name: 'Careers', path: '/careers' },
        { name: 'Press', path: '/press' },
        { name: 'Blog', path: '/blog' }
      ]
    },
    {
      title: 'Account',
      links: [
        { name: 'Sign In', path: '/login' },
        { name: 'Create Account', path: '/signup' },
        { name: 'My Orders', path: '/orders' },
        { name: 'Wishlist', path: '/wishlist' },
        { name: 'Account Settings', path: '/profile' },
        { name: 'Loyalty Program', path: '/loyalty' }
      ]
    }
  ];

  const socialLinks = [
    { name: 'Instagram', icon: '📷', url: 'https://instagram.com/nagma' },
    { name: 'Facebook', icon: '📘', url: 'https://facebook.com/nagma' },
    { name: 'Twitter', icon: '🐦', url: 'https://twitter.com/nagma' },
    { name: 'Pinterest', icon: '📌', url: 'https://pinterest.com/nagma' },
    { name: 'YouTube', icon: '📺', url: 'https://youtube.com/nagma' }
  ];

  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <Link to="/" className="flex items-center space-x-2 mb-4">
              <div className="text-2xl font-bold text-pink-400">Nagma</div>
              <div className="text-sm text-gray-400">Fashion</div>
            </Link>
            <p className="text-gray-400 text-sm mb-6">
              Curated fashion for the modern woman. Discover your unique style with our exclusive collections handpicked by influencer Nagma.
            </p>

            {/* Social Links */}
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-2xl hover:text-pink-400 transition-colors duration-300"
                  title={social.name}
                >
                  {social.icon}
                </a>
              ))}
            </div>
          </div>

          {/* Footer Links */}
          {footerSections.map((section) => (
            <div key={section.title} className="lg:col-span-1">
              <h3 className="text-lg font-semibold mb-4 text-pink-400">{section.title}</h3>
              <ul className="space-y-2">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <Link
                      to={link.path}
                      className="text-gray-400 hover:text-white transition-colors duration-300 text-sm"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Newsletter Signup */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="text-center">
            <h3 className="text-xl font-semibold mb-4 text-pink-400">Stay Connected</h3>
            <p className="text-gray-400 mb-6">
              Follow Nagma on social media for daily style inspiration and behind-the-scenes content
            </p>
            <div className="flex justify-center space-x-6 text-3xl">
              <span>✨</span>
              <span>👗</span>
              <span>💄</span>
              <span>👠</span>
              <span>💎</span>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-400 text-sm mb-4 md:mb-0">
              © {currentYear} Nagma Fashion. All rights reserved.
            </div>

            <div className="flex flex-wrap justify-center md:justify-end space-x-6 text-sm">
              <Link to="/privacy" className="text-gray-400 hover:text-white transition-colors duration-300">
                Privacy Policy
              </Link>
              <Link to="/terms" className="text-gray-400 hover:text-white transition-colors duration-300">
                Terms of Service
              </Link>
              <Link to="/cookies" className="text-gray-400 hover:text-white transition-colors duration-300">
                Cookie Policy
              </Link>
              <Link to="/accessibility" className="text-gray-400 hover:text-white transition-colors duration-300">
                Accessibility
              </Link>
            </div>
          </div>

          <div className="mt-4 text-center text-xs text-gray-500">
            Made with 💖 for fashion lovers everywhere
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
