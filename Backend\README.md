# Nagma Fashion Backend API

A comprehensive e-commerce backend API built with Node.js, Express, MongoDB, and various integrations for the Nagma Fashion platform.

## 🚀 Features

### Core Features
- **User Authentication & Authorization** (JWT + Google OAuth)
- **Product Management** with variants, images, and reviews
- **Category Management** with hierarchical structure
- **Order Management** with tracking and status updates
- **Payment Integration** with Razorpay
- **File Upload** with Cloudinary integration
- **Email Notifications** with custom templates
- **Admin Panel** with comprehensive dashboard
- **Reels/Video Content** management
- **Cart & Wishlist** functionality

### Security Features
- **Rate Limiting** to prevent abuse
- **Input Validation** with express-validator
- **Helmet** for security headers
- **CORS** configuration
- **Password Hashing** with bcrypt
- **JWT Token** authentication

### Integrations
- **MongoDB** with Mongoose ODM
- **Cloudinary** for image/video storage
- **Razorpay** for payment processing
- **Google OAuth** for social login
- **Nodemailer** for email services

## 📁 Project Structure

```
Backend/
├── config/
│   ├── cloudinary.js      # Cloudinary configuration
│   ├── db.js              # Database connection
│   └── passport.js        # Passport strategies
├── controllers/
│   └── user.controller.js # User controllers (legacy)
├── middleware/
│   └── auth.js            # Authentication middleware
├── models/
│   ├── User.js            # User model
│   ├── Product.js         # Product model
│   ├── Category.js        # Category model
│   ├── Order.js           # Order model
│   └── Reel.js            # Reel model
├── routes/
│   ├── auth.route.js      # Authentication routes
│   ├── user.route.js      # User management routes
│   ├── product.route.js   # Product routes
│   ├── category.route.js  # Category routes
│   ├── order.route.js     # Order routes
│   ├── admin.route.js     # Admin routes
│   ├── reel.route.js      # Reel routes
│   ├── upload.route.js    # File upload routes
│   ├── payment.route.js   # Payment routes
│   └── email.route.js     # Email routes
├── utils/
│   └── email.js           # Email utilities
├── app.js                 # Main application file
├── package.json           # Dependencies
└── .env.example           # Environment variables template
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or cloud)
- Cloudinary account
- Razorpay account
- Google OAuth credentials

### 1. Clone and Install
```bash
cd Backend
npm install
```

### 2. Environment Setup
Copy `.env.example` to `.env` and fill in your credentials:

```env
# Server Configuration
NODE_ENV=development
PORT=5000

# Database
MONGO_URI=mongodb://localhost:27017/nagma-fashion

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# Frontend URL
FRONTEND_URL=http://localhost:5174

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Email Configuration
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password-or-app-password
EMAIL_FROM=<EMAIL>

# Razorpay Configuration
RAZORPAY_KEY_ID=your-razorpay-key-id
RAZORPAY_KEY_SECRET=your-razorpay-key-secret
```

### 3. Start the Server
```bash
# Development
npm run dev

# Production
npm start
```

## 📚 API Documentation

### Base URL
```
http://localhost:5000/api
```

### Authentication Endpoints
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `GET /auth/google` - Google OAuth login
- `GET /auth/google/callback` - Google OAuth callback
- `POST /auth/verify-email` - Verify email address
- `POST /auth/forgot-password` - Request password reset
- `POST /auth/reset-password` - Reset password
- `GET /auth/me` - Get current user
- `POST /auth/logout` - Logout user

### Product Endpoints
- `GET /products` - Get all products (with filtering)
- `GET /products/:id` - Get single product
- `POST /products` - Create product (Admin)
- `PUT /products/:id` - Update product (Admin)
- `DELETE /products/:id` - Delete product (Admin)
- `POST /products/:id/reviews` - Add product review

### Category Endpoints
- `GET /categories` - Get all categories
- `GET /categories/tree` - Get category tree
- `GET /categories/:id` - Get single category
- `POST /categories` - Create category (Admin)
- `PUT /categories/:id` - Update category (Admin)
- `DELETE /categories/:id` - Delete category (Admin)

### Order Endpoints
- `GET /orders/my-orders` - Get user orders
- `GET /orders/:id` - Get single order
- `POST /orders` - Create new order
- `PATCH /orders/:id/status` - Update order status (Admin)
- `PATCH /orders/:id/cancel` - Cancel order

### User Endpoints
- `GET /users/profile` - Get user profile
- `PUT /users/profile` - Update user profile
- `POST /users/cart` - Add to cart
- `PUT /users/cart/:itemId` - Update cart item
- `DELETE /users/cart/:itemId` - Remove from cart
- `DELETE /users/cart` - Clear cart
- `POST /users/wishlist` - Add to wishlist
- `DELETE /users/wishlist/:productId` - Remove from wishlist
- `POST /users/addresses` - Add address
- `PUT /users/addresses/:addressId` - Update address
- `DELETE /users/addresses/:addressId` - Delete address

### Admin Endpoints
- `GET /admin/dashboard` - Get dashboard statistics
- `GET /admin/users` - Get all users
- `PATCH /admin/users/:id/status` - Update user status
- `GET /admin/orders` - Get all orders

### Payment Endpoints
- `POST /payment/create-order` - Create Razorpay order
- `POST /payment/verify-payment` - Verify payment
- `POST /payment/payment-failed` - Handle payment failure
- `POST /payment/webhook` - Razorpay webhook
- `GET /payment/status/:orderId` - Get payment status

### Reel Endpoints
- `GET /reels` - Get all reels
- `GET /reels/:id` - Get single reel
- `POST /reels` - Create reel (Admin)
- `POST /reels/:id/like` - Like/unlike reel
- `POST /reels/:id/save` - Save/unsave reel
- `POST /reels/:id/comments` - Add comment
- `POST /reels/:id/share` - Increment share count

### Upload Endpoints
- `POST /upload/products` - Upload product images
- `POST /upload/reels` - Upload reel videos
- `POST /upload/categories` - Upload category images
- `POST /upload/avatar` - Upload user avatar
- `POST /upload/banners` - Upload banner images
- `DELETE /upload/:publicId` - Delete file from Cloudinary

### Email Endpoints
- `POST /email/newsletter` - Send newsletter (Admin)
- `POST /email/promotion` - Send promotional email (Admin)
- `POST /email/custom` - Send custom email (Admin)
- `POST /email/test` - Send test email (Admin)

## 🔐 Authentication

The API uses JWT tokens for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### User Roles
- **user** - Regular customer
- **admin** - Store administrator
- **superadmin** - Super administrator

## 📊 Database Models

### User Model
- Personal information (name, email, phone)
- Authentication (password, Google ID)
- Cart and wishlist
- Addresses
- Order history
- Preferences

### Product Model
- Basic information (name, description, price)
- Images and variants
- Category and tags
- Reviews and ratings
- Stock management
- SEO fields

### Order Model
- Order items and pricing
- Shipping and billing addresses
- Payment information
- Tracking and status updates
- Cancellation and returns

### Category Model
- Hierarchical structure
- Images and descriptions
- SEO optimization
- Product counts

### Reel Model
- Video content
- Engagement metrics (likes, views, shares)
- Comments and interactions
- Featured products

## 🚀 Deployment

### Environment Variables for Production
```env
NODE_ENV=production
PORT=5000
MONGO_URI=your-production-mongodb-uri
JWT_SECRET=your-production-jwt-secret
# ... other production credentials
```

### PM2 Configuration
```bash
npm install -g pm2
pm2 start app.js --name "nagma-fashion-api"
pm2 startup
pm2 save
```

## 🧪 Testing

```bash
# Run tests (when implemented)
npm test

# Test specific endpoints
curl -X GET http://localhost:5000/api/health
```

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is proprietary and confidential.

## 🆘 Support

For support and questions, contact the development team.

---

**Nagma Fashion Backend API** - Built with ❤️ for the fashion industry
