const mongoose = require('mongoose');

const orderItemSchema = new mongoose.Schema({
    product: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Product',
        required: true
    },
    name: {
        type: String,
        required: true
    },
    image: String,
    price: {
        type: Number,
        required: true
    },
    quantity: {
        type: Number,
        required: true,
        min: 1
    },
    size: String,
    color: String,
    sku: String
});

const shippingAddressSchema = new mongoose.Schema({
    fullName: {
        type: String,
        required: true
    },
    phone: {
        type: String,
        required: true
    },
    addressLine1: {
        type: String,
        required: true
    },
    addressLine2: String,
    city: {
        type: String,
        required: true
    },
    state: {
        type: String,
        required: true
    },
    pincode: {
        type: String,
        required: true
    },
    country: {
        type: String,
        default: 'India'
    }
});

const paymentSchema = new mongoose.Schema({
    method: {
        type: String,
        enum: ['razorpay', 'cod', 'wallet'],
        required: true
    },
    status: {
        type: String,
        enum: ['pending', 'completed', 'failed', 'refunded'],
        default: 'pending'
    },
    transactionId: String,
    razorpayOrderId: String,
    razorpayPaymentId: String,
    razorpaySignature: String,
    paidAt: Date,
    refundId: String,
    refundedAt: Date,
    refundAmount: Number
});

const trackingSchema = new mongoose.Schema({
    status: {
        type: String,
        enum: ['pending', 'confirmed', 'processing', 'shipped', 'out_for_delivery', 'delivered', 'cancelled', 'returned'],
        default: 'pending'
    },
    message: String,
    location: String,
    timestamp: {
        type: Date,
        default: Date.now
    },
    updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }
});

const orderSchema = new mongoose.Schema({
    orderNumber: {
        type: String,
        unique: true,
        required: true
    },
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    items: [orderItemSchema],
    shippingAddress: shippingAddressSchema,
    billingAddress: shippingAddressSchema,
    subtotal: {
        type: Number,
        required: true
    },
    shippingCost: {
        type: Number,
        default: 0
    },
    tax: {
        type: Number,
        default: 0
    },
    discount: {
        type: Number,
        default: 0
    },
    couponCode: String,
    total: {
        type: Number,
        required: true
    },
    currency: {
        type: String,
        default: 'INR'
    },
    status: {
        type: String,
        enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned'],
        default: 'pending'
    },
    payment: paymentSchema,
    tracking: [trackingSchema],
    notes: String,
    adminNotes: String,
    estimatedDelivery: Date,
    deliveredAt: Date,
    cancelledAt: Date,
    cancellationReason: String,
    returnRequested: {
        type: Boolean,
        default: false
    },
    returnReason: String,
    returnRequestedAt: Date,
    returnApproved: {
        type: Boolean,
        default: false
    },
    returnApprovedAt: Date,
    refundProcessed: {
        type: Boolean,
        default: false
    },
    refundProcessedAt: Date
}, {
    timestamps: true
});

// Indexes
orderSchema.index({ orderNumber: 1 });
orderSchema.index({ user: 1 });
orderSchema.index({ status: 1 });
orderSchema.index({ 'payment.status': 1 });
orderSchema.index({ createdAt: -1 });

// Generate order number before saving
orderSchema.pre('save', function(next) {
    if (!this.orderNumber) {
        const timestamp = Date.now().toString();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        this.orderNumber = `NF${timestamp.slice(-6)}${random}`;
    }
    next();
});

// Add tracking update
orderSchema.methods.addTracking = function(status, message, location, updatedBy) {
    this.tracking.push({
        status,
        message,
        location,
        updatedBy,
        timestamp: new Date()
    });
    
    this.status = status;
    
    if (status === 'delivered') {
        this.deliveredAt = new Date();
    } else if (status === 'cancelled') {
        this.cancelledAt = new Date();
    }
};

// Calculate totals
orderSchema.methods.calculateTotals = function() {
    this.subtotal = this.items.reduce((total, item) => total + (item.price * item.quantity), 0);
    this.total = this.subtotal + this.shippingCost + this.tax - this.discount;
};

// Get current tracking status
orderSchema.virtual('currentTracking').get(function() {
    return this.tracking.length > 0 ? this.tracking[this.tracking.length - 1] : null;
});

// Check if order can be cancelled
orderSchema.virtual('canCancel').get(function() {
    return ['pending', 'confirmed'].includes(this.status);
});

// Check if order can be returned
orderSchema.virtual('canReturn').get(function() {
    return this.status === 'delivered' && !this.returnRequested;
});

module.exports = mongoose.model('Order', orderSchema);
