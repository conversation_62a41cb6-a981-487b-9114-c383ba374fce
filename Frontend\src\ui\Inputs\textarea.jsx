import React, { forwardRef } from 'react';
import { cn } from '../../utils/cn';

const TextArea = forwardRef(({
  className,
  variant = 'default',
  size = 'md',
  label,
  placeholder,
  helperText,
  error,
  disabled = false,
  rows = 4,
  resize = 'vertical',
  onFocus,
  onBlur,
  ...props
}, ref) => {

  const handleFocus = (e) => {
    onFocus?.(e);
  };

  const handleBlur = (e) => {
    onBlur?.(e);
  };

  // Size variants
  const sizeClasses = {
    sm: 'p-3 text-sm',
    md: 'p-4 text-base',
    lg: 'p-5 text-lg'
  };

  // Resize options
  const resizeClasses = {
    none: 'resize-none',
    vertical: 'resize-y',
    horizontal: 'resize-x',
    both: 'resize'
  };

  // Variant styles
  const variantClasses = {
    default: `
      border border-gray-300 bg-white
      focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20
      hover:border-gray-400
      disabled:bg-gray-50 disabled:border-gray-200 disabled:text-gray-500
    `,
    outlined: `
      border-2 border-gray-300 bg-transparent
      focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20
      hover:border-gray-400
      disabled:bg-gray-50 disabled:border-gray-200 disabled:text-gray-500
    `,
    filled: `
      border-0 bg-gray-100
      focus:bg-white focus:ring-2 focus:ring-blue-500/20 focus:shadow-md
      hover:bg-gray-200
      disabled:bg-gray-50 disabled:text-gray-500
    `
  };

  // Error state styles
  const errorClasses = error ? `
    border-red-500 focus:border-red-500 focus:ring-red-500/20
    hover:border-red-600
  ` : '';

  // Base textarea classes
  const baseClasses = `
    w-full rounded-lg font-medium transition-all duration-200
    placeholder:text-gray-500 placeholder:font-normal
    disabled:cursor-not-allowed disabled:opacity-60
    outline-none min-h-[100px]
  `;

  const textareaClasses = cn(
    baseClasses,
    variantClasses[variant],
    sizeClasses[size],
    resizeClasses[resize],
    errorClasses,
    className
  );

  return (
    <div className="w-full">
      {label && (
        <label className={cn(
          "block text-sm font-semibold mb-2 transition-colors duration-200",
          error ? "text-red-600" : "text-gray-700",
          disabled && "text-gray-500"
        )}>
          {label}
        </label>
      )}
      
      <textarea
        ref={ref}
        rows={rows}
        placeholder={placeholder}
        disabled={disabled}
        onFocus={handleFocus}
        onBlur={handleBlur}
        className={textareaClasses}
        {...props}
      />
      
      {(helperText || error) && (
        <p className={cn(
          "mt-2 text-sm transition-colors duration-200",
          error ? "text-red-600" : "text-gray-600"
        )}>
          {error || helperText}
        </p>
      )}
    </div>
  );
});

TextArea.displayName = 'TextArea';

export default TextArea;
