import React, { forwardRef } from 'react';
import { cn } from '../../utils/cn';

const Input = forwardRef(({
  className,
  type = 'text',
  variant = 'default',
  size = 'md',
  label,
  placeholder,
  helperText,
  error,
  disabled = false,
  leftIcon,
  rightIcon,
  onFocus,
  onBlur,
  ...props
}, ref) => {
  const handleFocus = (e) => {
    onFocus?.(e);
  };

  const handleBlur = (e) => {
    onBlur?.(e);
  };

  // Size variants
  const sizeClasses = {
    sm: 'h-9 px-3 text-sm',
    md: 'h-11 px-4 text-base',
    lg: 'h-13 px-5 text-lg'
  };

  // Variant styles
  const variantClasses = {
    default: `
      border border-gray-300 bg-white
      focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20
      hover:border-gray-400
      disabled:bg-gray-50 disabled:border-gray-200 disabled:text-gray-500
    `,
    outlined: `
      border-2 border-gray-300 bg-transparent
      focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20
      hover:border-gray-400
      disabled:bg-gray-50 disabled:border-gray-200 disabled:text-gray-500
    `,
    filled: `
      border-0 bg-gray-100
      focus:bg-white focus:ring-2 focus:ring-blue-500/20 focus:shadow-md
      hover:bg-gray-200
      disabled:bg-gray-50 disabled:text-gray-500
    `,
    underlined: `
      border-0 border-b-2 border-gray-300 bg-transparent rounded-none px-0
      focus:border-blue-500 focus:ring-0
      hover:border-gray-400
      disabled:border-gray-200 disabled:text-gray-500
    `
  };

  // Error state styles
  const errorClasses = error ? `
    border-red-500 focus:border-red-500 focus:ring-red-500/20
    hover:border-red-600
  ` : '';

  // Base input classes
  const baseClasses = `
    w-full rounded-lg font-medium transition-all duration-200
    placeholder:text-gray-500 placeholder:font-normal
    disabled:cursor-not-allowed disabled:opacity-60
    outline-none
  `;

  // Icon classes
  const iconClasses = 'absolute top-1/2 transform -translate-y-1/2 text-gray-500 pointer-events-none';
  const leftIconClasses = `${iconClasses} left-3`;
  const rightIconClasses = `${iconClasses} right-3`;

  // Adjust padding when icons are present
  const paddingWithIcons = cn(
    sizeClasses[size],
    leftIcon && 'pl-10',
    rightIcon && 'pr-10'
  );

  const inputClasses = cn(
    baseClasses,
    variantClasses[variant],
    paddingWithIcons,
    errorClasses,
    className
  );

  return (
    <div className="w-full">
      {label && (
        <label className={cn(
          "block text-sm font-semibold mb-2 transition-colors duration-200",
          error ? "text-red-600" : "text-gray-700",
          disabled && "text-gray-500"
        )}>
          {label}
        </label>
      )}

      <div className="relative">
        {leftIcon && (
          <div className={leftIconClasses}>
            {leftIcon}
          </div>
        )}

        <input
          ref={ref}
          type={type}
          placeholder={placeholder}
          disabled={disabled}
          onFocus={handleFocus}
          onBlur={handleBlur}
          className={inputClasses}
          {...props}
        />

        {rightIcon && (
          <div className={rightIconClasses}>
            {rightIcon}
          </div>
        )}
      </div>

      {(helperText || error) && (
        <p className={cn(
          "mt-2 text-sm transition-colors duration-200",
          error ? "text-red-600" : "text-gray-600"
        )}>
          {error || helperText}
        </p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;
