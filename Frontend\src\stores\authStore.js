import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authAPI, setAuthToken } from '../services/api';
import toast from 'react-hot-toast';

const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials) => {
        set({ isLoading: true, error: null });
        try {
          const response = await authAPI.login(credentials);
          const { token, user } = response.data;
          
          setAuthToken(token);
          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null
          });
          
          toast.success('Login successful!');
          return { success: true, user };
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Login failed';
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage
          });
          return { success: false, error: errorMessage };
        }
      },

      register: async (userData) => {
        set({ isLoading: true, error: null });
        try {
          const response = await authAPI.register(userData);
          const { token, user } = response.data;
          
          setAuthToken(token);
          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null
          });
          
          toast.success('Registration successful!');
          return { success: true, user };
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Registration failed';
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage
          });
          return { success: false, error: errorMessage };
        }
      },

      logout: async () => {
        try {
          await authAPI.logout();
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          setAuthToken(null);
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          });
          toast.success('Logged out successfully');
        }
      },

      getCurrentUser: async () => {
        const { token } = get();
        if (!token) return;

        set({ isLoading: true });
        try {
          const response = await authAPI.getMe();
          const { user } = response.data;
          
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null
          });
          
          return { success: true, user };
        } catch (error) {
          console.error('Get current user error:', error);
          // Token might be invalid, clear auth state
          get().logout();
          return { success: false, error: error.response?.data?.message };
        }
      },

      updateUser: (userData) => {
        set((state) => ({
          user: { ...state.user, ...userData }
        }));
      },

      verifyEmail: async (token) => {
        set({ isLoading: true, error: null });
        try {
          await authAPI.verifyEmail(token);
          set((state) => ({
            user: state.user ? { ...state.user, isEmailVerified: true } : null,
            isLoading: false,
            error: null
          }));
          toast.success('Email verified successfully!');
          return { success: true };
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Email verification failed';
          set({ isLoading: false, error: errorMessage });
          return { success: false, error: errorMessage };
        }
      },

      forgotPassword: async (email) => {
        set({ isLoading: true, error: null });
        try {
          await authAPI.forgotPassword(email);
          set({ isLoading: false, error: null });
          toast.success('Password reset link sent to your email');
          return { success: true };
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Failed to send reset link';
          set({ isLoading: false, error: errorMessage });
          return { success: false, error: errorMessage };
        }
      },

      resetPassword: async (data) => {
        set({ isLoading: true, error: null });
        try {
          await authAPI.resetPassword(data);
          set({ isLoading: false, error: null });
          toast.success('Password reset successful!');
          return { success: true };
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Password reset failed';
          set({ isLoading: false, error: errorMessage });
          return { success: false, error: errorMessage };
        }
      },

      clearError: () => {
        set({ error: null });
      },

      // Getters
      isAdmin: () => {
        const { user } = get();
        return user && ['admin', 'superadmin'].includes(user.role);
      },

      isSuperAdmin: () => {
        const { user } = get();
        return user && user.role === 'superadmin';
      },

      hasPermission: (permission) => {
        const { user } = get();
        if (!user) return false;
        
        // Super admin has all permissions
        if (user.role === 'superadmin') return true;
        
        // Admin has most permissions
        if (user.role === 'admin') {
          const adminPermissions = [
            'manage_products',
            'manage_categories',
            'manage_orders',
            'view_dashboard',
            'manage_reels',
            'send_emails'
          ];
          return adminPermissions.includes(permission);
        }
        
        // Regular users have limited permissions
        const userPermissions = [
          'view_profile',
          'edit_profile',
          'place_orders',
          'view_orders'
        ];
        return userPermissions.includes(permission);
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      }),
      onRehydrateStorage: () => (state) => {
        if (state?.token) {
          setAuthToken(state.token);
          // Verify token is still valid
          state.getCurrentUser();
        }
      }
    }
  )
);

export default useAuthStore;
