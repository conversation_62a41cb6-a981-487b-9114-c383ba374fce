# UI Components Library

A beautiful collection of reusable React components built with Tailwind CSS.

## Components

### Input Component

A versatile input component with multiple variants, sizes, and states.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `'default' \| 'outlined' \| 'filled' \| 'underlined'` | `'default'` | Visual style variant |
| `size` | `'sm' \| 'md' \| 'lg'` | `'md'` | Size of the input |
| `label` | `string` | - | Label text |
| `placeholder` | `string` | - | Placeholder text |
| `helperText` | `string` | - | Helper text below input |
| `error` | `string` | - | Error message (overrides helperText) |
| `disabled` | `boolean` | `false` | Disabled state |
| `leftIcon` | `ReactNode` | - | Icon on the left side |
| `rightIcon` | `ReactNode` | - | Icon on the right side |

#### Usage

```jsx
import { Input } from '../ui';

// Basic usage
<Input 
  label="Email" 
  placeholder="Enter your email" 
  type="email" 
/>

// With icons
<Input 
  label="Search" 
  placeholder="Search..." 
  leftIcon={<SearchIcon />}
/>

// Error state
<Input 
  label="Username" 
  error="This field is required" 
/>
```

### TextArea Component

A flexible textarea component with similar styling to the Input component.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `'default' \| 'outlined' \| 'filled'` | `'default'` | Visual style variant |
| `size` | `'sm' \| 'md' \| 'lg'` | `'md'` | Size of the textarea |
| `rows` | `number` | `4` | Number of rows |
| `resize` | `'none' \| 'vertical' \| 'horizontal' \| 'both'` | `'vertical'` | Resize behavior |
| `label` | `string` | - | Label text |
| `placeholder` | `string` | - | Placeholder text |
| `helperText` | `string` | - | Helper text below textarea |
| `error` | `string` | - | Error message |
| `disabled` | `boolean` | `false` | Disabled state |

#### Usage

```jsx
import { TextArea } from '../ui';

<TextArea 
  label="Message" 
  placeholder="Enter your message..." 
  rows={4}
/>
```

### Button Component

A comprehensive button component with multiple variants, sizes, and states.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `'primary' \| 'secondary' \| 'outline' \| 'ghost' \| 'destructive' \| 'success' \| 'warning'` | `'primary'` | Visual style variant |
| `size` | `'sm' \| 'md' \| 'lg'` | `'md'` | Size of the button |
| `disabled` | `boolean` | `false` | Disabled state |
| `loading` | `boolean` | `false` | Loading state with spinner |
| `leftIcon` | `ReactNode` | - | Icon on the left side |
| `rightIcon` | `ReactNode` | - | Icon on the right side |
| `type` | `'button' \| 'submit' \| 'reset'` | `'button'` | Button type |

#### Usage

```jsx
import { Button } from '../ui';

// Basic usage
<Button variant="primary">
  Click me
</Button>

// With icons
<Button leftIcon={<PlusIcon />}>
  Add Item
</Button>

// Loading state
<Button loading={isLoading}>
  Submit
</Button>
```

## Styling

All components are built with Tailwind CSS and support:

- **Responsive design**: Components adapt to different screen sizes
- **Dark mode ready**: Easy to extend for dark mode support
- **Accessibility**: Proper ARIA attributes and keyboard navigation
- **Customization**: Override styles with the `className` prop

## Examples

Check out the `UIDemo` component for comprehensive examples of all variants and use cases.

## Utility Functions

### cn (className utility)

A utility function for conditionally merging class names.

```jsx
import { cn } from '../utils/cn';

const className = cn(
  'base-class',
  condition && 'conditional-class',
  'another-class'
);
```
