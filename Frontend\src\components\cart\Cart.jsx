import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon, MinusIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import useCartStore from '../../stores/cartStore';
import useAuthStore from '../../stores/authStore';
import { Button } from '../../ui';

const Cart = () => {
  const {
    items,
    isOpen,
    isLoading,
    closeCart,
    updateCartItem,
    removeFromCart,
    updateLocalCartItem,
    removeFromLocalCart,
    getCartSubtotal,
    getShippingCost,
    getTax,
    getFinalTotal,
    getCartCount
  } = useCartStore();

  const { isAuthenticated } = useAuthStore();

  const handleUpdateQuantity = async (item, newQuantity) => {
    if (newQuantity < 1) return;
    
    if (isAuthenticated) {
      await updateCartItem(item._id, newQuantity);
    } else {
      updateLocalCartItem(item._id, newQuantity);
    }
  };

  const handleRemoveItem = async (item) => {
    if (isAuthenticated) {
      await removeFromCart(item._id);
    } else {
      removeFromLocalCart(item._id);
    }
  };

  const subtotal = getCartSubtotal();
  const shipping = getShippingCost();
  const tax = getTax();
  const total = getFinalTotal();

  return (
    <Transition.Root show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={closeCart}>
        <Transition.Child
          as={Fragment}
          enter="ease-in-out duration-500"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in-out duration-500"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-in-out duration-500 sm:duration-700"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in-out duration-500 sm:duration-700"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="pointer-events-auto w-screen max-w-md">
                  <div className="flex h-full flex-col overflow-y-scroll bg-white shadow-xl">
                    {/* Header */}
                    <div className="flex-1 overflow-y-auto px-4 py-6 sm:px-6">
                      <div className="flex items-start justify-between">
                        <Dialog.Title className="text-lg font-medium text-gray-900">
                          Shopping Cart ({getCartCount()})
                        </Dialog.Title>
                        <div className="ml-3 flex h-7 items-center">
                          <button
                            type="button"
                            className="-m-2 p-2 text-gray-400 hover:text-gray-500"
                            onClick={closeCart}
                          >
                            <XMarkIcon className="h-6 w-6" />
                          </button>
                        </div>
                      </div>

                      {/* Cart Items */}
                      <div className="mt-8">
                        <div className="flow-root">
                          <AnimatePresence>
                            {items.length === 0 ? (
                              <motion.div
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                className="text-center py-12"
                              >
                                <div className="text-gray-400 mb-4">
                                  <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                  </svg>
                                </div>
                                <p className="text-gray-500">Your cart is empty</p>
                                <Button
                                  onClick={closeCart}
                                  className="mt-4"
                                  variant="outline"
                                >
                                  Continue Shopping
                                </Button>
                              </motion.div>
                            ) : (
                              <ul className="-my-6 divide-y divide-gray-200">
                                {items.map((item, index) => (
                                  <motion.li
                                    key={item._id}
                                    initial={{ opacity: 0, x: 20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    exit={{ opacity: 0, x: -20 }}
                                    transition={{ delay: index * 0.1 }}
                                    className="flex py-6"
                                  >
                                    <div className="h-24 w-24 flex-shrink-0 overflow-hidden rounded-md border border-gray-200">
                                      <img
                                        src={item.product.images?.[0]?.url || '/placeholder-image.jpg'}
                                        alt={item.product.name}
                                        className="h-full w-full object-cover object-center"
                                      />
                                    </div>

                                    <div className="ml-4 flex flex-1 flex-col">
                                      <div>
                                        <div className="flex justify-between text-base font-medium text-gray-900">
                                          <h3>
                                            <Link
                                              to={`/products/${item.product.slug || item.product._id}`}
                                              onClick={closeCart}
                                              className="hover:text-pink-600"
                                            >
                                              {item.product.name}
                                            </Link>
                                          </h3>
                                          <p className="ml-4">
                                            ₹{(item.product.salePrice || item.product.basePrice) * item.quantity}
                                          </p>
                                        </div>
                                        <div className="mt-1 text-sm text-gray-500">
                                          {item.size && <span>Size: {item.size}</span>}
                                          {item.size && item.color && <span> • </span>}
                                          {item.color && <span>Color: {item.color}</span>}
                                        </div>
                                        <div className="mt-1 text-sm text-gray-500">
                                          ₹{item.product.salePrice || item.product.basePrice} each
                                        </div>
                                      </div>
                                      <div className="flex flex-1 items-end justify-between text-sm">
                                        <div className="flex items-center space-x-2">
                                          <button
                                            onClick={() => handleUpdateQuantity(item, item.quantity - 1)}
                                            disabled={item.quantity <= 1 || isLoading}
                                            className="p-1 rounded-full hover:bg-gray-100 disabled:opacity-50"
                                          >
                                            <MinusIcon className="h-4 w-4" />
                                          </button>
                                          <span className="font-medium">{item.quantity}</span>
                                          <button
                                            onClick={() => handleUpdateQuantity(item, item.quantity + 1)}
                                            disabled={isLoading}
                                            className="p-1 rounded-full hover:bg-gray-100"
                                          >
                                            <PlusIcon className="h-4 w-4" />
                                          </button>
                                        </div>

                                        <div className="flex">
                                          <button
                                            type="button"
                                            onClick={() => handleRemoveItem(item)}
                                            disabled={isLoading}
                                            className="font-medium text-red-600 hover:text-red-500 disabled:opacity-50"
                                          >
                                            <TrashIcon className="h-4 w-4" />
                                          </button>
                                        </div>
                                      </div>
                                    </div>
                                  </motion.li>
                                ))}
                              </ul>
                            )}
                          </AnimatePresence>
                        </div>
                      </div>
                    </div>

                    {/* Footer */}
                    {items.length > 0 && (
                      <div className="border-t border-gray-200 px-4 py-6 sm:px-6">
                        {/* Order Summary */}
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>Subtotal</span>
                            <span>₹{subtotal}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Shipping</span>
                            <span>{shipping === 0 ? 'Free' : `₹${shipping}`}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Tax (GST)</span>
                            <span>₹{tax}</span>
                          </div>
                          <div className="border-t border-gray-200 pt-2">
                            <div className="flex justify-between text-base font-medium text-gray-900">
                              <span>Total</span>
                              <span>₹{total}</span>
                            </div>
                          </div>
                        </div>

                        {shipping > 0 && (
                          <p className="mt-2 text-xs text-gray-500">
                            Add ₹{500 - subtotal} more for free shipping!
                          </p>
                        )}

                        <div className="mt-6">
                          <Link
                            to="/checkout"
                            onClick={closeCart}
                            className="flex items-center justify-center rounded-md border border-transparent bg-pink-600 px-6 py-3 text-base font-medium text-white shadow-sm hover:bg-pink-700 transition-colors"
                          >
                            Checkout
                          </Link>
                        </div>
                        <div className="mt-6 flex justify-center text-center text-sm text-gray-500">
                          <p>
                            or{' '}
                            <button
                              type="button"
                              className="font-medium text-pink-600 hover:text-pink-500"
                              onClick={closeCart}
                            >
                              Continue Shopping
                              <span aria-hidden="true"> &rarr;</span>
                            </button>
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default Cart;
