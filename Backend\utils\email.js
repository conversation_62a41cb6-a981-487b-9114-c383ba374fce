const nodemailer = require('nodemailer');

// Create transporter
const createTransporter = () => {
    if (process.env.NODE_ENV === 'production') {
        // Production email service (Gmail, SendGrid, etc.)
        return nodemailer.createTransporter({
            service: 'gmail',
            auth: {
                user: process.env.EMAIL_USER,
                pass: process.env.EMAIL_PASS
            }
        });
    } else {
        // Development - use Ethereal Email for testing
        return nodemailer.createTransporter({
            host: 'smtp.ethereal.email',
            port: 587,
            auth: {
                user: process.env.EMAIL_USER || '<EMAIL>',
                pass: process.env.EMAIL_PASS || 'ethereal.pass'
            }
        });
    }
};

// Email templates
const templates = {
    welcome: (data) => ({
        subject: 'Welcome to Nagma Fashion! 👗',
        html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #e91e63; margin: 0;">Nagma Fashion</h1>
                    <p style="color: #666; margin: 5px 0;">Your Style, Our Passion</p>
                </div>
                
                <div style="background: linear-gradient(135deg, #e91e63, #9c27b0); padding: 30px; border-radius: 10px; color: white; text-align: center; margin-bottom: 30px;">
                    <h2 style="margin: 0 0 15px 0;">Welcome, ${data.name}! 🎉</h2>
                    <p style="margin: 0; font-size: 16px;">Thank you for joining the Nagma Fashion family!</p>
                </div>
                
                <div style="padding: 20px; background: #f9f9f9; border-radius: 10px; margin-bottom: 30px;">
                    <h3 style="color: #333; margin-top: 0;">What's Next?</h3>
                    <ul style="color: #666; line-height: 1.6;">
                        <li>Verify your email address to unlock all features</li>
                        <li>Explore our latest fashion collections</li>
                        <li>Follow Nagma's reels for style inspiration</li>
                        <li>Get exclusive access to sales and new arrivals</li>
                    </ul>
                </div>
                
                <div style="text-align: center; margin-bottom: 30px;">
                    <a href="${data.verificationUrl}" style="background: #e91e63; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; display: inline-block;">
                        Verify Email Address
                    </a>
                </div>
                
                <div style="text-align: center; color: #666; font-size: 14px;">
                    <p>If you didn't create this account, please ignore this email.</p>
                    <p>© 2024 Nagma Fashion. All rights reserved.</p>
                </div>
            </div>
        `
    }),

    orderConfirmation: (data) => ({
        subject: `Order Confirmed #${data.orderNumber} - Nagma Fashion`,
        html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #e91e63; margin: 0;">Nagma Fashion</h1>
                </div>
                
                <div style="background: #4caf50; padding: 20px; border-radius: 10px; color: white; text-align: center; margin-bottom: 30px;">
                    <h2 style="margin: 0;">Order Confirmed! ✅</h2>
                    <p style="margin: 10px 0 0 0;">Order #${data.orderNumber}</p>
                </div>
                
                <div style="padding: 20px; border: 1px solid #ddd; border-radius: 10px; margin-bottom: 20px;">
                    <h3 style="margin-top: 0; color: #333;">Order Details</h3>
                    <p><strong>Order Date:</strong> ${new Date(data.orderDate).toLocaleDateString()}</p>
                    <p><strong>Total Amount:</strong> ₹${data.total}</p>
                    <p><strong>Payment Method:</strong> ${data.paymentMethod}</p>
                    <p><strong>Estimated Delivery:</strong> ${data.estimatedDelivery}</p>
                </div>
                
                <div style="padding: 20px; border: 1px solid #ddd; border-radius: 10px; margin-bottom: 20px;">
                    <h3 style="margin-top: 0; color: #333;">Items Ordered</h3>
                    ${data.items.map(item => `
                        <div style="border-bottom: 1px solid #eee; padding: 10px 0;">
                            <p style="margin: 0; font-weight: bold;">${item.name}</p>
                            <p style="margin: 5px 0; color: #666;">Quantity: ${item.quantity} | Price: ₹${item.price}</p>
                        </div>
                    `).join('')}
                </div>
                
                <div style="text-align: center; margin-bottom: 30px;">
                    <a href="${process.env.FRONTEND_URL}/orders/${data.orderId}" style="background: #e91e63; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; display: inline-block;">
                        Track Your Order
                    </a>
                </div>
                
                <div style="text-align: center; color: #666; font-size: 14px;">
                    <p>Thank you for shopping with Nagma Fashion!</p>
                    <p>© 2024 Nagma Fashion. All rights reserved.</p>
                </div>
            </div>
        `
    }),

    passwordReset: (data) => ({
        subject: 'Password Reset Request - Nagma Fashion',
        html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #e91e63; margin: 0;">Nagma Fashion</h1>
                </div>
                
                <div style="background: #ff9800; padding: 20px; border-radius: 10px; color: white; text-align: center; margin-bottom: 30px;">
                    <h2 style="margin: 0;">Password Reset Request 🔐</h2>
                </div>
                
                <div style="padding: 20px; background: #f9f9f9; border-radius: 10px; margin-bottom: 30px;">
                    <p>Hi ${data.name},</p>
                    <p>We received a request to reset your password for your Nagma Fashion account.</p>
                    <p>Click the button below to reset your password. This link will expire in 10 minutes.</p>
                </div>
                
                <div style="text-align: center; margin-bottom: 30px;">
                    <a href="${data.resetUrl}" style="background: #e91e63; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; display: inline-block;">
                        Reset Password
                    </a>
                </div>
                
                <div style="text-align: center; color: #666; font-size: 14px;">
                    <p>If you didn't request this password reset, please ignore this email.</p>
                    <p>© 2024 Nagma Fashion. All rights reserved.</p>
                </div>
            </div>
        `
    }),

    orderShipped: (data) => ({
        subject: `Your Order is Shipped! #${data.orderNumber} - Nagma Fashion`,
        html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #e91e63; margin: 0;">Nagma Fashion</h1>
                </div>
                
                <div style="background: #2196f3; padding: 20px; border-radius: 10px; color: white; text-align: center; margin-bottom: 30px;">
                    <h2 style="margin: 0;">Your Order is on the Way! 🚚</h2>
                    <p style="margin: 10px 0 0 0;">Order #${data.orderNumber}</p>
                </div>
                
                <div style="padding: 20px; border: 1px solid #ddd; border-radius: 10px; margin-bottom: 20px;">
                    <h3 style="margin-top: 0; color: #333;">Shipping Details</h3>
                    <p><strong>Tracking Number:</strong> ${data.trackingNumber || 'Will be updated soon'}</p>
                    <p><strong>Estimated Delivery:</strong> ${data.estimatedDelivery}</p>
                    <p><strong>Shipping Address:</strong></p>
                    <div style="background: #f9f9f9; padding: 10px; border-radius: 5px;">
                        ${data.shippingAddress}
                    </div>
                </div>
                
                <div style="text-align: center; margin-bottom: 30px;">
                    <a href="${process.env.FRONTEND_URL}/orders/${data.orderId}" style="background: #e91e63; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; display: inline-block;">
                        Track Your Order
                    </a>
                </div>
                
                <div style="text-align: center; color: #666; font-size: 14px;">
                    <p>Thank you for shopping with Nagma Fashion!</p>
                    <p>© 2024 Nagma Fashion. All rights reserved.</p>
                </div>
            </div>
        `
    })
};

// Send email function
const sendEmail = async ({ to, subject, template, data, html, text }) => {
    try {
        const transporter = createTransporter();

        let emailContent = {};

        if (template && templates[template]) {
            emailContent = templates[template](data);
        } else if (html || text) {
            emailContent = { subject, html, text };
        } else {
            throw new Error('No email content provided');
        }

        const mailOptions = {
            from: `"Nagma Fashion" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
            to,
            subject: emailContent.subject || subject,
            html: emailContent.html,
            text: emailContent.text
        };

        const result = await transporter.sendMail(mailOptions);
        
        console.log('Email sent successfully:', result.messageId);
        
        if (process.env.NODE_ENV === 'development') {
            console.log('Preview URL:', nodemailer.getTestMessageUrl(result));
        }

        return result;
    } catch (error) {
        console.error('Email sending failed:', error);
        throw error;
    }
};

// Send bulk emails
const sendBulkEmail = async (emails) => {
    try {
        const results = await Promise.allSettled(
            emails.map(email => sendEmail(email))
        );

        const successful = results.filter(result => result.status === 'fulfilled').length;
        const failed = results.filter(result => result.status === 'rejected').length;

        console.log(`Bulk email results: ${successful} successful, ${failed} failed`);
        
        return { successful, failed, results };
    } catch (error) {
        console.error('Bulk email sending failed:', error);
        throw error;
    }
};

module.exports = {
    sendEmail,
    sendBulkEmail,
    templates
};
