const express = require('express');
const { body, query, validationResult } = require('express-validator');
const Product = require('../models/Product');
const Category = require('../models/Category');
const { auth, adminAuth, optionalAuth } = require('../middleware/auth');
const { uploadProduct } = require('../config/cloudinary');

const router = express.Router();

// Get all products with filtering, sorting, and pagination
router.get('/', [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('sort').optional().isIn(['newest', 'oldest', 'price_low', 'price_high', 'rating', 'popular']).withMessage('Invalid sort option'),
    query('category').optional().isMongoId().withMessage('Invalid category ID'),
    query('minPrice').optional().isFloat({ min: 0 }).withMessage('Min price must be a positive number'),
    query('maxPrice').optional().isFloat({ min: 0 }).withMessage('Max price must be a positive number'),
    query('inStock').optional().isBoolean().withMessage('In stock must be a boolean'),
    query('featured').optional().isBoolean().withMessage('Featured must be a boolean'),
    query('onSale').optional().isBoolean().withMessage('On sale must be a boolean'),
    query('search').optional().isLength({ min: 1 }).withMessage('Search query cannot be empty')
], optionalAuth, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const {
            page = 1,
            limit = 12,
            sort = 'newest',
            category,
            minPrice,
            maxPrice,
            inStock,
            featured,
            onSale,
            search,
            tags
        } = req.query;

        // Build filter object
        const filter = { isActive: true };

        if (category) filter.category = category;
        if (featured !== undefined) filter.isFeatured = featured === 'true';
        if (onSale !== undefined) filter.isOnSale = onSale === 'true';
        if (inStock === 'true') filter.totalStock = { $gt: 0 };

        // Price range filter
        if (minPrice || maxPrice) {
            filter.basePrice = {};
            if (minPrice) filter.basePrice.$gte = parseFloat(minPrice);
            if (maxPrice) filter.basePrice.$lte = parseFloat(maxPrice);
        }

        // Search filter
        if (search) {
            filter.$text = { $search: search };
        }

        // Tags filter
        if (tags) {
            const tagArray = tags.split(',').map(tag => tag.trim());
            filter.tags = { $in: tagArray };
        }

        // Build sort object
        let sortObj = {};
        switch (sort) {
            case 'newest':
                sortObj = { createdAt: -1 };
                break;
            case 'oldest':
                sortObj = { createdAt: 1 };
                break;
            case 'price_low':
                sortObj = { basePrice: 1 };
                break;
            case 'price_high':
                sortObj = { basePrice: -1 };
                break;
            case 'rating':
                sortObj = { 'rating.average': -1 };
                break;
            case 'popular':
                sortObj = { soldCount: -1 };
                break;
            default:
                sortObj = { createdAt: -1 };
        }

        // Add text score for search results
        if (search) {
            sortObj.score = { $meta: 'textScore' };
        }

        const skip = (parseInt(page) - 1) * parseInt(limit);

        // Execute query
        const products = await Product.find(filter)
            .populate('category', 'name slug')
            .sort(sortObj)
            .skip(skip)
            .limit(parseInt(limit))
            .lean();

        // Get total count for pagination
        const total = await Product.countDocuments(filter);

        // Calculate pagination info
        const totalPages = Math.ceil(total / parseInt(limit));
        const hasNextPage = parseInt(page) < totalPages;
        const hasPrevPage = parseInt(page) > 1;

        res.json({
            success: true,
            data: {
                products,
                pagination: {
                    currentPage: parseInt(page),
                    totalPages,
                    totalProducts: total,
                    hasNextPage,
                    hasPrevPage,
                    limit: parseInt(limit)
                },
                filters: {
                    category,
                    minPrice,
                    maxPrice,
                    inStock,
                    featured,
                    onSale,
                    search,
                    tags,
                    sort
                }
            }
        });

    } catch (error) {
        console.error('Get products error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch products',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Get single product by ID or slug
router.get('/:identifier', optionalAuth, async (req, res) => {
    try {
        const { identifier } = req.params;
        
        // Check if identifier is ObjectId or slug
        const isObjectId = /^[0-9a-fA-F]{24}$/.test(identifier);
        const filter = isObjectId ? { _id: identifier } : { slug: identifier };
        filter.isActive = true;

        const product = await Product.findOne(filter)
            .populate('category', 'name slug')
            .populate('reviews.user', 'name avatar')
            .populate('createdBy', 'name')
            .lean();

        if (!product) {
            return res.status(404).json({
                success: false,
                message: 'Product not found'
            });
        }

        // Increment view count (don't await to avoid slowing response)
        Product.findByIdAndUpdate(product._id, { $inc: { viewCount: 1 } }).exec();

        // Get related products
        const relatedProducts = await Product.find({
            category: product.category._id,
            _id: { $ne: product._id },
            isActive: true
        })
        .populate('category', 'name slug')
        .limit(4)
        .lean();

        res.json({
            success: true,
            data: {
                product,
                relatedProducts
            }
        });

    } catch (error) {
        console.error('Get product error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch product',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Create new product (Admin only)
router.post('/', adminAuth, uploadProduct.array('images', 10), [
    body('name').trim().isLength({ min: 2 }).withMessage('Product name must be at least 2 characters'),
    body('description').trim().isLength({ min: 10 }).withMessage('Description must be at least 10 characters'),
    body('shortDescription').trim().isLength({ min: 5, max: 200 }).withMessage('Short description must be 5-200 characters'),
    body('category').isMongoId().withMessage('Valid category ID is required'),
    body('basePrice').isFloat({ min: 0 }).withMessage('Base price must be a positive number'),
    body('variants').optional().isArray().withMessage('Variants must be an array'),
    body('tags').optional().isArray().withMessage('Tags must be an array')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const {
            name,
            description,
            shortDescription,
            category,
            subcategory,
            brand,
            basePrice,
            salePrice,
            tags,
            features,
            specifications,
            variants,
            seo
        } = req.body;

        // Verify category exists
        const categoryExists = await Category.findById(category);
        if (!categoryExists) {
            return res.status(400).json({
                success: false,
                message: 'Category not found'
            });
        }

        // Process uploaded images
        const images = req.files ? req.files.map((file, index) => ({
            url: file.path,
            alt: `${name} - Image ${index + 1}`,
            isPrimary: index === 0
        })) : [];

        // Create product
        const product = new Product({
            name,
            description,
            shortDescription,
            category,
            subcategory,
            brand: brand || 'Nagma Fashion',
            images,
            basePrice: parseFloat(basePrice),
            salePrice: salePrice ? parseFloat(salePrice) : undefined,
            tags: tags ? JSON.parse(tags) : [],
            features: features ? JSON.parse(features) : [],
            specifications: specifications ? JSON.parse(specifications) : {},
            variants: variants ? JSON.parse(variants) : [],
            seo: seo ? JSON.parse(seo) : {},
            createdBy: req.user.id
        });

        await product.save();

        // Populate category for response
        await product.populate('category', 'name slug');

        res.status(201).json({
            success: true,
            message: 'Product created successfully',
            data: { product }
        });

    } catch (error) {
        console.error('Create product error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create product',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Add product review
router.post('/:id/reviews', auth, [
    body('rating').isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
    body('comment').trim().isLength({ min: 5 }).withMessage('Comment must be at least 5 characters')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { id } = req.params;
        const { rating, comment } = req.body;

        const product = await Product.findById(id);
        if (!product) {
            return res.status(404).json({
                success: false,
                message: 'Product not found'
            });
        }

        // Check if user already reviewed this product
        const existingReview = product.reviews.find(
            review => review.user.toString() === req.user.id
        );

        if (existingReview) {
            return res.status(400).json({
                success: false,
                message: 'You have already reviewed this product'
            });
        }

        // Add review
        product.reviews.push({
            user: req.user.id,
            rating: parseInt(rating),
            comment
        });

        // Recalculate rating
        product.calculateRating();

        await product.save();

        // Populate the new review
        await product.populate('reviews.user', 'name avatar');

        res.status(201).json({
            success: true,
            message: 'Review added successfully',
            data: {
                review: product.reviews[product.reviews.length - 1],
                rating: product.rating
            }
        });

    } catch (error) {
        console.error('Add review error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to add review',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Update product (Admin only)
router.put('/:id', adminAuth, uploadProduct.array('images', 10), async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = { ...req.body };

        // Parse JSON fields if they exist
        if (updateData.tags) updateData.tags = JSON.parse(updateData.tags);
        if (updateData.features) updateData.features = JSON.parse(updateData.features);
        if (updateData.specifications) updateData.specifications = JSON.parse(updateData.specifications);
        if (updateData.variants) updateData.variants = JSON.parse(updateData.variants);
        if (updateData.seo) updateData.seo = JSON.parse(updateData.seo);

        // Process new images if uploaded
        if (req.files && req.files.length > 0) {
            const newImages = req.files.map((file, index) => ({
                url: file.path,
                alt: `${updateData.name || 'Product'} - Image ${index + 1}`,
                isPrimary: index === 0
            }));
            updateData.images = newImages;
        }

        updateData.updatedBy = req.user.id;

        const product = await Product.findByIdAndUpdate(id, updateData, {
            new: true,
            runValidators: true
        }).populate('category', 'name slug');

        if (!product) {
            return res.status(404).json({
                success: false,
                message: 'Product not found'
            });
        }

        res.json({
            success: true,
            message: 'Product updated successfully',
            data: { product }
        });

    } catch (error) {
        console.error('Update product error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update product',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Delete product (Admin only)
router.delete('/:id', adminAuth, async (req, res) => {
    try {
        const { id } = req.params;

        const product = await Product.findByIdAndDelete(id);
        if (!product) {
            return res.status(404).json({
                success: false,
                message: 'Product not found'
            });
        }

        res.json({
            success: true,
            message: 'Product deleted successfully'
        });

    } catch (error) {
        console.error('Delete product error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete product',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

module.exports = router;
