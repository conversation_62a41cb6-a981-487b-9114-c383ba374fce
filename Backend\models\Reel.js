const mongoose = require('mongoose');

const commentSchema = new mongoose.Schema({
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    text: {
        type: String,
        required: true,
        trim: true
    },
    likes: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }],
    replies: [{
        user: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        text: {
            type: String,
            required: true,
            trim: true
        },
        createdAt: {
            type: Date,
            default: Date.now
        }
    }],
    createdAt: {
        type: Date,
        default: Date.now
    }
});

const reelSchema = new mongoose.Schema({
    title: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        trim: true
    },
    video: {
        url: {
            type: String,
            required: true
        },
        publicId: String,
        duration: Number, // in seconds
        thumbnail: String
    },
    tags: [String],
    hashtags: [String],
    category: {
        type: String,
        enum: ['fashion', 'styling', 'tutorial', 'behind_scenes', 'product_showcase', 'trend_alert'],
        default: 'fashion'
    },
    featuredProducts: [{
        product: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Product'
        },
        timestamp: Number, // When product appears in video (seconds)
        position: {
            x: Number,
            y: Number
        }
    }],
    likes: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }],
    comments: [commentSchema],
    shares: {
        type: Number,
        default: 0
    },
    views: {
        type: Number,
        default: 0
    },
    saves: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }],
    isActive: {
        type: Boolean,
        default: true
    },
    isFeatured: {
        type: Boolean,
        default: false
    },
    isPinned: {
        type: Boolean,
        default: false
    },
    scheduledAt: Date,
    publishedAt: Date,
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }
}, {
    timestamps: true
});

// Indexes
reelSchema.index({ isActive: 1 });
reelSchema.index({ isFeatured: 1 });
reelSchema.index({ isPinned: 1 });
reelSchema.index({ category: 1 });
reelSchema.index({ views: -1 });
reelSchema.index({ createdAt: -1 });
reelSchema.index({ publishedAt: -1 });
reelSchema.index({ tags: 1 });
reelSchema.index({ hashtags: 1 });

// Virtual for like count
reelSchema.virtual('likeCount').get(function() {
    return this.likes.length;
});

// Virtual for comment count
reelSchema.virtual('commentCount').get(function() {
    return this.comments.length;
});

// Virtual for save count
reelSchema.virtual('saveCount').get(function() {
    return this.saves.length;
});

// Virtual for engagement rate
reelSchema.virtual('engagementRate').get(function() {
    if (this.views === 0) return 0;
    const engagements = this.likes.length + this.comments.length + this.shares + this.saves.length;
    return Math.round((engagements / this.views) * 100 * 100) / 100; // Percentage with 2 decimal places
});

// Method to increment views
reelSchema.methods.incrementViews = function() {
    this.views += 1;
    return this.save();
};

// Method to toggle like
reelSchema.methods.toggleLike = function(userId) {
    const likeIndex = this.likes.indexOf(userId);
    if (likeIndex > -1) {
        this.likes.splice(likeIndex, 1);
        return false; // Unliked
    } else {
        this.likes.push(userId);
        return true; // Liked
    }
};

// Method to toggle save
reelSchema.methods.toggleSave = function(userId) {
    const saveIndex = this.saves.indexOf(userId);
    if (saveIndex > -1) {
        this.saves.splice(saveIndex, 1);
        return false; // Unsaved
    } else {
        this.saves.push(userId);
        return true; // Saved
    }
};

// Method to add comment
reelSchema.methods.addComment = function(userId, text) {
    this.comments.push({
        user: userId,
        text: text
    });
    return this.save();
};

// Method to increment shares
reelSchema.methods.incrementShares = function() {
    this.shares += 1;
    return this.save();
};

// Transform JSON output
reelSchema.methods.toJSON = function() {
    const reel = this.toObject({ virtuals: true });
    return reel;
};

module.exports = mongoose.model('Reel', reelSchema);
