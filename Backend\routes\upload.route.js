const express = require('express');
const { adminAuth } = require('../middleware/auth');
const { 
    uploadProduct, 
    uploadReel, 
    uploadCategory, 
    uploadAvatar, 
    uploadBanner,
    deleteFromCloudinary 
} = require('../config/cloudinary');

const router = express.Router();

// Upload product images
router.post('/products', adminAuth, uploadProduct.array('images', 10), async (req, res) => {
    try {
        if (!req.files || req.files.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No files uploaded'
            });
        }

        const uploadedFiles = req.files.map((file, index) => ({
            url: file.path,
            publicId: file.filename,
            alt: `Product image ${index + 1}`,
            isPrimary: index === 0
        }));

        res.json({
            success: true,
            message: 'Product images uploaded successfully',
            data: { images: uploadedFiles }
        });

    } catch (error) {
        console.error('Upload product images error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to upload product images',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Upload reel video
router.post('/reels', adminAuth, uploadReel.single('video'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: 'No video file uploaded'
            });
        }

        const uploadedFile = {
            url: req.file.path,
            publicId: req.file.filename,
            duration: req.file.duration || 0,
            thumbnail: req.file.eager ? req.file.eager[0].secure_url : null
        };

        res.json({
            success: true,
            message: 'Reel video uploaded successfully',
            data: { video: uploadedFile }
        });

    } catch (error) {
        console.error('Upload reel video error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to upload reel video',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Upload category image
router.post('/categories', adminAuth, uploadCategory.single('image'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: 'No image file uploaded'
            });
        }

        const uploadedFile = {
            url: req.file.path,
            publicId: req.file.filename,
            alt: 'Category image'
        };

        res.json({
            success: true,
            message: 'Category image uploaded successfully',
            data: { image: uploadedFile }
        });

    } catch (error) {
        console.error('Upload category image error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to upload category image',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Upload user avatar
router.post('/avatar', adminAuth, uploadAvatar.single('avatar'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: 'No avatar file uploaded'
            });
        }

        const uploadedFile = {
            url: req.file.path,
            publicId: req.file.filename,
            alt: 'User avatar'
        };

        res.json({
            success: true,
            message: 'Avatar uploaded successfully',
            data: { avatar: uploadedFile }
        });

    } catch (error) {
        console.error('Upload avatar error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to upload avatar',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Upload banner/hero images
router.post('/banners', adminAuth, uploadBanner.array('banners', 5), async (req, res) => {
    try {
        if (!req.files || req.files.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No files uploaded'
            });
        }

        const uploadedFiles = req.files.map((file, index) => ({
            url: file.path,
            publicId: file.filename,
            alt: `Banner image ${index + 1}`
        }));

        res.json({
            success: true,
            message: 'Banner images uploaded successfully',
            data: { banners: uploadedFiles }
        });

    } catch (error) {
        console.error('Upload banner images error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to upload banner images',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Delete file from Cloudinary
router.delete('/:publicId', adminAuth, async (req, res) => {
    try {
        const { publicId } = req.params;
        const { resourceType = 'image' } = req.query;

        if (!publicId) {
            return res.status(400).json({
                success: false,
                message: 'Public ID is required'
            });
        }

        const result = await deleteFromCloudinary(publicId, resourceType);

        if (result.result === 'ok') {
            res.json({
                success: true,
                message: 'File deleted successfully',
                data: { result }
            });
        } else {
            res.status(400).json({
                success: false,
                message: 'Failed to delete file',
                data: { result }
            });
        }

    } catch (error) {
        console.error('Delete file error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete file',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Upload multiple files (generic)
router.post('/multiple', adminAuth, uploadProduct.array('files', 20), async (req, res) => {
    try {
        if (!req.files || req.files.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No files uploaded'
            });
        }

        const uploadedFiles = req.files.map((file, index) => ({
            url: file.path,
            publicId: file.filename,
            originalName: file.originalname,
            size: file.size,
            format: file.format,
            resourceType: file.resource_type
        }));

        res.json({
            success: true,
            message: `${uploadedFiles.length} files uploaded successfully`,
            data: { files: uploadedFiles }
        });

    } catch (error) {
        console.error('Upload multiple files error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to upload files',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Get upload statistics
router.get('/stats', adminAuth, async (req, res) => {
    try {
        // This would typically query your database for upload statistics
        // For now, we'll return mock data
        const stats = {
            totalUploads: 0,
            totalSize: 0,
            byType: {
                images: 0,
                videos: 0,
                documents: 0
            },
            byCategory: {
                products: 0,
                reels: 0,
                categories: 0,
                banners: 0,
                avatars: 0
            },
            recentUploads: []
        };

        res.json({
            success: true,
            data: { stats }
        });

    } catch (error) {
        console.error('Get upload stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get upload statistics',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

module.exports = router;
