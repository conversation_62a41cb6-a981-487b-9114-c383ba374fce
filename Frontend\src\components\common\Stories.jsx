import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const Stories = () => {
  const navigate = useNavigate();
  const [viewedStories, setViewedStories] = useState(new Set());

  const stories = [
    {
      id: 1,
      title: "Today's OOTD",
      thumbnail: "👗",
      gradient: "from-pink-400 to-rose-500",
      isNew: true
    },
    {
      id: 2,
      title: "Behind Scenes",
      thumbnail: "📸",
      gradient: "from-purple-400 to-indigo-500",
      isNew: true
    },
    {
      id: 3,
      title: "Shopping Haul",
      thumbnail: "🛍️",
      gradient: "from-blue-400 to-cyan-500",
      isNew: false
    },
    {
      id: 4,
      title: "Style Tips",
      thumbnail: "✨",
      gradient: "from-green-400 to-emerald-500",
      isNew: true
    },
    {
      id: 5,
      title: "Q&A Session",
      thumbnail: "💬",
      gradient: "from-yellow-400 to-orange-500",
      isNew: false
    },
    {
      id: 6,
      title: "Workout Fit",
      thumbnail: "💪",
      gradient: "from-red-400 to-pink-500",
      isNew: true
    }
  ];

  const handleStoryClick = (storyId) => {
    setViewedStories(prev => new Set([...prev, storyId]));
    navigate('/reels');
  };

  return (
    <div className="bg-white py-8 border-b border-gray-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900">Nagma's Stories</h2>
          <button 
            onClick={() => navigate('/reels')}
            className="text-pink-600 hover:text-pink-700 font-medium text-sm"
          >
            View All
          </button>
        </div>

        <div className="flex space-x-4 overflow-x-auto pb-2 scrollbar-hide">
          {stories.map((story) => (
            <div
              key={story.id}
              onClick={() => handleStoryClick(story.id)}
              className="flex-shrink-0 cursor-pointer group"
            >
              <div className="relative">
                {/* Story Ring */}
                <div className={`w-16 h-16 rounded-full p-0.5 ${
                  viewedStories.has(story.id) 
                    ? 'bg-gray-300' 
                    : `bg-gradient-to-tr ${story.gradient}`
                } group-hover:scale-105 transition-transform duration-200`}>
                  <div className="w-full h-full bg-white rounded-full flex items-center justify-center">
                    <span className="text-2xl">{story.thumbnail}</span>
                  </div>
                </div>

                {/* New Indicator */}
                {story.isNew && !viewedStories.has(story.id) && (
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                )}
              </div>

              {/* Story Title */}
              <p className="text-xs text-gray-600 text-center mt-2 max-w-[64px] truncate">
                {story.title}
              </p>
            </div>
          ))}

          {/* Add Story Button */}
          <div className="flex-shrink-0 cursor-pointer group">
            <div className="w-16 h-16 rounded-full border-2 border-dashed border-gray-300 flex items-center justify-center group-hover:border-pink-400 transition-colors duration-200">
              <span className="text-2xl text-gray-400 group-hover:text-pink-400">➕</span>
            </div>
            <p className="text-xs text-gray-600 text-center mt-2 max-w-[64px] truncate">
              Your Story
            </p>
          </div>
        </div>

        {/* Mobile Swipe Indicator */}
        <div className="flex justify-center mt-4 lg:hidden">
          <div className="flex space-x-1">
            <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
            <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
            <div className="w-1 h-1 bg-pink-400 rounded-full"></div>
            <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
            <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Stories;
